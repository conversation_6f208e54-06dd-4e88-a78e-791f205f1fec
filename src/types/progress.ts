// Progress Tracking Types
export interface SectionProgress {
  id: string;
  title: string;
  description: string;
  totalSubsections: number;
  completedSubsections: number;
  progressPercentage: number;
  status: 'not_started' | 'in_progress' | 'completed';
  lastUpdated: string;
  updatedBy: string;
}

// Task Mapping Types for Integration between doc-2 and doc-3
export interface TaskMapping {
  id: string;
  sourceDocumentId: string; // doc-2
  sourceSectionId: string;
  sourceSubsectionId: string;
  targetDocumentId: string; // doc-3
  targetSectionId: string;
  targetSubsectionId?: string;
  mappingType: 'one_to_one' | 'one_to_many' | 'many_to_one';
  createdAt: string;
  createdBy: string;
  isActive: boolean;
}

export interface TaskMappingProgress {
  mappingId: string;
  sourceProgress: number;
  targetProgress: number;
  lastSyncAt: string;
  syncStatus: 'synced' | 'pending' | 'error';
}

export interface IntegratedSubsection {
  id: string;
  title: string;
  description: string;
  type: 'upload' | 'form';
  formUrl?: string;
  mappedToClausul?: string; // ID of the target section in doc-3
  mappedToEvidence?: string; // ID of the target subsection in doc-3
  createdAt: string;
  createdBy: string;
}

export interface DocumentProgress {
  id: string;
  title: string;
  description: string;
  sections: SectionProgress[];
  totalSections: number;
  completedSections: number;
  overallProgress: number;
  status: 'not_started' | 'in_progress' | 'completed';
  lastUpdated: string;
}

export interface ProgressSummary {
  totalDocuments: number;
  completedDocuments: number;
  inProgressDocuments: number;
  notStartedDocuments: number;
  overallProgress: number;
  documents: DocumentProgress[];
}

export interface ProgressStats {
  totalSections: number;
  completedSections: number;
  totalSubsections: number;
  completedSubsections: number;
  averageProgress: number;
}
