import { useState, useEffect, useCallback } from 'react';
import { useProgressStore } from '@/store/progress';
import { useTaskMappingStore } from '@/store/taskMapping';

export interface SubsectionAction {
  subsectionId: string;
  actionType: 'file_upload' | 'form_submit' | 'content_add' | 'manual_complete';
  timestamp: string;
  userId: string;
  userName: string;
  details?: string;
}

export interface SectionProgress {
  sectionId: string;
  sectionTitle: string;
  totalSubsections: number;
  completedSubsections: number;
  progress: number;
  actions: SubsectionAction[];
}

export interface DocumentProgress {
  documentId: string;
  totalSubsections: number;
  completedSubsections: number;
  progress: number;
  sections: SectionProgress[];
  lastUpdated: string;
}

export function useSubsectionActions(documentId: string) {
  const { addOrUpdateSection, updateSectionProgress } = useProgressStore();
  const { getMappingsBySource, getMappingsByTarget, syncProgress } = useTaskMappingStore();
  const [documentProgress, setDocumentProgress] = useState<DocumentProgress | null>(null);

  // Get storage key for actions
  const getActionsStorageKey = () => `${documentId}-subsection-actions`;
  const getFilesStorageKey = () => `${documentId}-section-files`;

  // Load actions from localStorage
  const loadActions = useCallback((): SubsectionAction[] => {
    const saved = localStorage.getItem(getActionsStorageKey());
    return saved ? JSON.parse(saved) : [];
  }, [documentId]);

  // Save actions to localStorage
  const saveActions = useCallback((actions: SubsectionAction[]) => {
    localStorage.setItem(getActionsStorageKey(), JSON.stringify(actions));
    // Trigger custom event for real-time updates
    window.dispatchEvent(new CustomEvent('subsectionActionsChange', { 
      detail: { documentId, actions } 
    }));
  }, [documentId]);

  // Add action for subsection
  const addSubsectionAction = useCallback((
    subsectionId: string,
    actionType: SubsectionAction['actionType'],
    userId: string,
    userName: string,
    details?: string
  ) => {
    const actions = loadActions();
    
    // Check if action already exists for this subsection
    const existingActionIndex = actions.findIndex(action => action.subsectionId === subsectionId);
    
    const newAction: SubsectionAction = {
      subsectionId,
      actionType,
      timestamp: new Date().toISOString(),
      userId,
      userName,
      details
    };

    if (existingActionIndex >= 0) {
      // Update existing action
      actions[existingActionIndex] = newAction;
    } else {
      // Add new action
      actions.push(newAction);
    }

    saveActions(actions);
    calculateProgress();

    // Sync with task mappings if this is a source document
    syncWithTaskMappings(subsectionId, actionType);
  }, [loadActions, saveActions]);

  // Sync progress with task mappings
  const syncWithTaskMappings = useCallback((subsectionId: string, actionType: SubsectionAction['actionType']) => {
    // Find mappings where this subsection is the source
    const mappings = getMappingsBySource(documentId);

    mappings.forEach(mapping => {
      if (mapping.sourceSubsectionId === subsectionId) {
        // Sync progress to target (100% if action exists, 0% if removed)
        const progress = actionType === 'manual_complete' ? 0 : 100;
        syncProgress(mapping.id, progress);

        // Trigger event for real-time updates
        window.dispatchEvent(new CustomEvent('taskMappingSync', {
          detail: {
            mappingId: mapping.id,
            sourceDocumentId: documentId,
            sourceSubsectionId: subsectionId,
            targetDocumentId: mapping.targetDocumentId,
            targetSectionId: mapping.targetSectionId,
            progress
          }
        }));
      }
    });
  }, [documentId, getMappingsBySource, syncProgress]);

  // Remove action for subsection
  const removeSubsectionAction = useCallback((subsectionId: string) => {
    const actions = loadActions();
    const filteredActions = actions.filter(action => action.subsectionId !== subsectionId);
    saveActions(filteredActions);
    calculateProgress();

    // Sync removal with task mappings
    syncWithTaskMappings(subsectionId, 'manual_complete');
  }, [loadActions, saveActions, syncWithTaskMappings]);

  // Check if subsection has action
  const hasSubsectionAction = useCallback((subsectionId: string): boolean => {
    const actions = loadActions();
    return actions.some(action => action.subsectionId === subsectionId);
  }, [loadActions]);

  // Get subsection action details
  const getSubsectionAction = useCallback((subsectionId: string): SubsectionAction | null => {
    const actions = loadActions();
    return actions.find(action => action.subsectionId === subsectionId) || null;
  }, [loadActions]);

  // Calculate progress based on actions and files with fair section weighting
  const calculateProgress = useCallback(() => {
    // Get dynamic sections from localStorage (since we can't use hooks conditionally)
    const sectionsKey = `${documentId}-dynamic-sections`;
    const subsectionsKey = `${documentId}-dynamic-subsections`;

    const savedSections = localStorage.getItem(sectionsKey);
    const savedSubsections = localStorage.getItem(subsectionsKey);

    const dynamicSections = savedSections ? JSON.parse(savedSections) : [];
    const dynamicSubSections = savedSubsections ? JSON.parse(savedSubsections) : {};

    console.log(`[${documentId}] Dynamic sections:`, dynamicSections);
    console.log(`[${documentId}] Dynamic subsections:`, dynamicSubSections);

    // Get actions and files
    const actions = loadActions();
    const savedFiles = localStorage.getItem(getFilesStorageKey());
    const sectionFiles = savedFiles ? JSON.parse(savedFiles) : {};

    console.log(`[${documentId}] Actions:`, actions);
    console.log(`[${documentId}] Section files:`, sectionFiles);

    const sectionsProgress: SectionProgress[] = [];
    const totalSections = dynamicSections.length;

    // Calculate progress for each section with equal weighting
    dynamicSections.forEach((section: any) => {
      const subSections = dynamicSubSections[section.id] || [];
      const sectionTotalSubsections = subSections.length;
      let sectionCompletedSubsections = 0;
      const sectionActions: SubsectionAction[] = [];

      console.log(`[${documentId}] Section ${section.id}:`, {
        title: section.title,
        totalSubsections: sectionTotalSubsections,
        subSections: subSections.map((s: any) => s.id)
      });

      // Check each subsection for actions or files
      subSections.forEach((subSection: any) => {
        let hasAction = false;

        // Check for explicit actions
        const action = actions.find(a => a.subsectionId === subSection.id);
        if (action) {
          hasAction = true;
          sectionActions.push(action);
          console.log(`[${documentId}] Found action for ${subSection.id}:`, action);
        }

        // Check for uploaded files (also counts as action)
        const files = sectionFiles[subSection.id] || [];
        if (files.length > 0 && !action) {
          // Create implicit file upload action
          const fileAction: SubsectionAction = {
            subsectionId: subSection.id,
            actionType: 'file_upload',
            timestamp: new Date().toISOString(),
            userId: 'system',
            userName: 'System',
            details: `${files.length} file(s) uploaded`
          };
          hasAction = true;
          sectionActions.push(fileAction);
          console.log(`[${documentId}] Found files for ${subSection.id}:`, files);
        }

        if (hasAction) {
          sectionCompletedSubsections++;
        }
      });

      // Calculate section progress (0-100%)
      const sectionProgress = sectionTotalSubsections > 0
        ? Math.round((sectionCompletedSubsections / sectionTotalSubsections) * 100)
        : 0;

      console.log(`[${documentId}] Section ${section.id} progress:`, {
        completed: sectionCompletedSubsections,
        total: sectionTotalSubsections,
        progress: sectionProgress
      });

      sectionsProgress.push({
        sectionId: section.id,
        sectionTitle: section.title,
        totalSubsections: sectionTotalSubsections,
        completedSubsections: sectionCompletedSubsections,
        progress: sectionProgress,
        actions: sectionActions
      });

      // Update progress store for this section
      if (sectionTotalSubsections > 0) {
        addOrUpdateSection(documentId, section.id, section.title, sectionTotalSubsections);
        updateSectionProgress(documentId, section.id, sectionCompletedSubsections, 'System');
      }
    });

    // Calculate overall progress with equal section weighting
    // Each section contributes equally to the total progress
    const overallProgress = totalSections > 0
      ? Math.round(sectionsProgress.reduce((sum, section) => sum + section.progress, 0) / totalSections)
      : 0;

    console.log(`[${documentId}] Overall progress calculation:`, {
      totalSections,
      sectionProgresses: sectionsProgress.map(s => ({ id: s.sectionId, progress: s.progress })),
      overallProgress
    });

    // Calculate total subsections and completed subsections
    const totalSubsections = sectionsProgress.reduce((sum, section) => sum + section.totalSubsections, 0);
    const completedSubsections = sectionsProgress.reduce((sum, section) => sum + section.completedSubsections, 0);

    const newDocumentProgress: DocumentProgress = {
      documentId,
      totalSubsections,
      completedSubsections,
      progress: overallProgress,
      sections: sectionsProgress,
      lastUpdated: new Date().toISOString()
    };

    setDocumentProgress(newDocumentProgress);
    return newDocumentProgress;
  }, [documentId, addOrUpdateSection, updateSectionProgress, loadActions]);

  // Listen for changes
  useEffect(() => {
    const handleActionsChange = () => {
      console.log(`[${documentId}] Actions changed, recalculating progress...`);
      calculateProgress();
    };

    const handleStorageChange = (e: StorageEvent) => {
      if (e.key === getActionsStorageKey() || e.key === getFilesStorageKey()) {
        console.log(`[${documentId}] Storage changed for key: ${e.key}, recalculating progress...`);
        calculateProgress();
      }
    };

    // Listen for task mapping sync events
    const handleTaskMappingSync = (event: CustomEvent) => {
      const { targetDocumentId, targetSectionId, progress } = event.detail;

      // If this document is the target, create/update action
      if (targetDocumentId === documentId) {
        console.log(`[${documentId}] Received sync from task mapping for section: ${targetSectionId}, progress: ${progress}`);

        if (progress === 100) {
          // Create action for the target subsection
          addSubsectionAction(
            targetSectionId,
            'content_add',
            'system',
            'System Sync',
            'Synced from source document'
          );
        } else {
          // Remove action if progress is 0
          removeSubsectionAction(targetSectionId);
        }
      }
    };

    // Listen for custom events
    window.addEventListener('subsectionActionsChange', handleActionsChange);
    window.addEventListener('localStorageChange', handleActionsChange);
    window.addEventListener('storage', handleStorageChange);
    window.addEventListener('taskMappingSync', handleTaskMappingSync as EventListener);

    // Initial calculation
    calculateProgress();

    return () => {
      window.removeEventListener('subsectionActionsChange', handleActionsChange);
      window.removeEventListener('localStorageChange', handleActionsChange);
      window.removeEventListener('storage', handleStorageChange);
      window.removeEventListener('taskMappingSync', handleTaskMappingSync as EventListener);
    };
  }, [calculateProgress, addSubsectionAction, removeSubsectionAction]);

  // Also recalculate when files change (for real-time updates)
  useEffect(() => {
    const interval = setInterval(() => {
      calculateProgress();
    }, 1000); // Check every second for file changes

    return () => clearInterval(interval);
  }, [calculateProgress]);

  return {
    documentProgress,
    addSubsectionAction,
    removeSubsectionAction,
    hasSubsectionAction,
    getSubsectionAction,
    refreshProgress: calculateProgress
  };
}

// Hook for tracking actions across all documents
export function useAllDocumentsActions() {
  const doc1Actions = useSubsectionActions('doc-1');
  const doc2Actions = useSubsectionActions('doc-2');
  const doc3Actions = useSubsectionActions('doc-3');
  const doc4Actions = useSubsectionActions('doc-4');

  const getAllProgress = useCallback(() => {
    return {
      'doc-1': doc1Actions.documentProgress,
      'doc-2': doc2Actions.documentProgress,
      'doc-3': doc3Actions.documentProgress,
      'doc-4': doc4Actions.documentProgress
    };
  }, [
    doc1Actions.documentProgress,
    doc2Actions.documentProgress,
    doc3Actions.documentProgress,
    doc4Actions.documentProgress
  ]);

  const refreshAllProgress = useCallback(() => {
    doc1Actions.refreshProgress();
    doc2Actions.refreshProgress();
    doc3Actions.refreshProgress();
    doc4Actions.refreshProgress();
  }, [doc1Actions, doc2Actions, doc3Actions, doc4Actions]);

  return {
    getAllProgress,
    refreshAllProgress,
    doc1: doc1Actions,
    doc2: doc2Actions,
    doc3: doc3Actions,
    doc4: doc4Actions
  };
}
