import { useState, useEffect, useCallback } from 'react';
import { useTaskMappingStore } from '@/store/taskMapping';
import { useSubsectionActions } from './useSubsectionActions';
import { useProgressStore } from '@/store/progress';
import { TaskMapping, IntegratedSubsection } from '@/types/progress';

export interface TaskIntegrationOptions {
  sourceDocumentId: string;
  targetDocumentId: string;
  autoSync?: boolean;
}

export function useTaskIntegration(options: TaskIntegrationOptions) {
  const {
    createMapping,
    getMappingsBySource,
    getMappingsByTarget,
    syncProgress,
    getMappingProgress,
    isSubsectionMapped,
    getMappedTargets,
    getMappedSources
  } = useTaskMappingStore();

  const { updateSectionProgress } = useProgressStore();
  const sourceActions = useSubsectionActions(options.sourceDocumentId);
  const targetActions = useSubsectionActions(options.targetDocumentId);

  const [isLoading, setIsLoading] = useState(false);

  // Create mapping between source task and target evidence
  const createTaskMapping = useCallback((
    sourceSectionId: string,
    sourceSubsectionId: string,
    targetSectionId: string,
    targetSubsectionId: string,
    mappingType: 'one_to_one' | 'one_to_many' | 'many_to_one' = 'one_to_one',
    createdBy: string
  ) => {
    return createMapping({
      sourceDocumentId: options.sourceDocumentId,
      sourceSectionId,
      sourceSubsectionId,
      targetDocumentId: options.targetDocumentId,
      targetSectionId,
      targetSubsectionId,
      mappingType,
      createdBy,
      isActive: true
    });
  }, [createMapping, options.sourceDocumentId, options.targetDocumentId]);

  // Sync progress from source to target
  const syncTaskProgress = useCallback((
    sourceSectionId: string,
    sourceSubsectionId: string
  ) => {
    const mappings = getMappedTargets(
      options.sourceDocumentId,
      sourceSectionId,
      sourceSubsectionId
    );

    mappings.forEach(mapping => {
      // Get source progress (check if subsection has been completed)
      const sourceProgress = sourceActions.hasSubsectionAction(sourceSubsectionId) ? 100 : 0;
      
      // Sync to target
      syncProgress(mapping.id, sourceProgress);
      
      // Update target document progress
      if (sourceProgress === 100) {
        targetActions.addSubsectionAction(
          mapping.targetSubsectionId || mapping.targetSectionId,
          'content_add',
          'system',
          'System Sync',
          `Synced from ${options.sourceDocumentId}/${sourceSectionId}/${sourceSubsectionId}`
        );
      }
    });
  }, [
    getMappedTargets,
    options.sourceDocumentId,
    sourceActions,
    targetActions,
    syncProgress
  ]);

  // Get all mappings for a section
  const getSectionMappings = useCallback((
    sectionId: string,
    direction: 'source' | 'target' = 'source'
  ) => {
    if (direction === 'source') {
      return getMappingsBySource(options.sourceDocumentId, sectionId);
    } else {
      return getMappingsByTarget(options.targetDocumentId, sectionId);
    }
  }, [getMappingsBySource, getMappingsByTarget, options.sourceDocumentId, options.targetDocumentId]);

  // Check if a subsection is mapped
  const checkSubsectionMapping = useCallback((
    sectionId: string,
    subsectionId: string,
    direction: 'source' | 'target' = 'source'
  ) => {
    const documentId = direction === 'source' ? options.sourceDocumentId : options.targetDocumentId;
    return isSubsectionMapped(documentId, sectionId, subsectionId);
  }, [isSubsectionMapped, options.sourceDocumentId, options.targetDocumentId]);

  // Get mapping details for a subsection
  const getSubsectionMappings = useCallback((
    sectionId: string,
    subsectionId: string,
    direction: 'source' | 'target' = 'source'
  ) => {
    if (direction === 'source') {
      return getMappedTargets(options.sourceDocumentId, sectionId, subsectionId);
    } else {
      return getMappedSources(options.targetDocumentId, sectionId, subsectionId);
    }
  }, [getMappedTargets, getMappedSources, options.sourceDocumentId, options.targetDocumentId]);

  // Calculate integrated progress for a section
  const calculateIntegratedProgress = useCallback((sectionId: string) => {
    const mappings = getSectionMappings(sectionId, 'source');
    
    if (mappings.length === 0) {
      return { progress: 0, mappedCount: 0, completedCount: 0 };
    }

    let completedCount = 0;
    mappings.forEach(mapping => {
      const progress = getMappingProgress(mapping.id);
      if (progress && progress.sourceProgress === 100) {
        completedCount++;
      }
    });

    const progress = Math.round((completedCount / mappings.length) * 100);
    
    return {
      progress,
      mappedCount: mappings.length,
      completedCount
    };
  }, [getSectionMappings, getMappingProgress]);

  // Auto-sync when source actions change
  useEffect(() => {
    if (!options.autoSync) return;

    const handleSourceActionChange = (event: CustomEvent) => {
      const { documentId, actions } = event.detail;
      
      if (documentId === options.sourceDocumentId) {
        // Find mappings for changed actions and sync them
        actions.forEach((action: any) => {
          const mappings = getMappedTargets(
            options.sourceDocumentId,
            action.sectionId || 'unknown',
            action.subsectionId
          );
          
          mappings.forEach(mapping => {
            syncProgress(mapping.id, 100); // Action exists = 100% progress
          });
        });
      }
    };

    window.addEventListener('subsectionActionsChange', handleSourceActionChange as EventListener);
    
    return () => {
      window.removeEventListener('subsectionActionsChange', handleSourceActionChange as EventListener);
    };
  }, [options.autoSync, options.sourceDocumentId, getMappedTargets, syncProgress]);

  // Bulk create mappings for a section
  const createSectionMappings = useCallback(async (
    sourceSectionId: string,
    targetSectionId: string,
    subsectionMappings: Array<{
      sourceSubsectionId: string;
      targetSubsectionId: string;
      mappingType?: 'one_to_one' | 'one_to_many' | 'many_to_one';
    }>,
    createdBy: string
  ) => {
    setIsLoading(true);
    
    try {
      const mappingPromises = subsectionMappings.map(mapping =>
        createTaskMapping(
          sourceSectionId,
          mapping.sourceSubsectionId,
          targetSectionId,
          mapping.targetSubsectionId,
          mapping.mappingType || 'one_to_one',
          createdBy
        )
      );

      const mappingIds = await Promise.all(mappingPromises);
      return mappingIds;
    } finally {
      setIsLoading(false);
    }
  }, [createTaskMapping]);

  return {
    // Mapping Management
    createTaskMapping,
    createSectionMappings,
    
    // Progress Sync
    syncTaskProgress,
    calculateIntegratedProgress,
    
    // Query Functions
    getSectionMappings,
    getSubsectionMappings,
    checkSubsectionMapping,
    
    // State
    isLoading,
    
    // Store Access
    sourceActions,
    targetActions
  };
}

// Hook specifically for doc-2 to doc-3 integration
export function useProcedureToEvidenceIntegration() {
  return useTaskIntegration({
    sourceDocumentId: 'doc-2',
    targetDocumentId: 'doc-3',
    autoSync: true
  });
}

// Hook for getting integration status
export function useIntegrationStatus(documentId: string, sectionId: string) {
  const { getMappingsBySource, getMappingsByTarget, getMappingProgress } = useTaskMappingStore();
  const [status, setStatus] = useState({
    isMapped: false,
    mappingCount: 0,
    syncedCount: 0,
    pendingCount: 0,
    lastSyncAt: null as string | null
  });

  useEffect(() => {
    const sourceMappings = getMappingsBySource(documentId, sectionId);
    const targetMappings = getMappingsByTarget(documentId, sectionId);
    const allMappings = [...sourceMappings, ...targetMappings];
    
    let syncedCount = 0;
    let pendingCount = 0;
    let lastSyncAt: string | null = null;

    allMappings.forEach(mapping => {
      const progress = getMappingProgress(mapping.id);
      if (progress) {
        if (progress.syncStatus === 'synced') {
          syncedCount++;
        } else if (progress.syncStatus === 'pending') {
          pendingCount++;
        }
        
        if (!lastSyncAt || progress.lastSyncAt > lastSyncAt) {
          lastSyncAt = progress.lastSyncAt;
        }
      }
    });

    setStatus({
      isMapped: allMappings.length > 0,
      mappingCount: allMappings.length,
      syncedCount,
      pendingCount,
      lastSyncAt
    });
  }, [documentId, sectionId, getMappingsBySource, getMappingsByTarget, getMappingProgress]);

  return status;
}
