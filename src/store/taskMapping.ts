import { create } from 'zustand';
import { persist } from 'zustand/middleware';
import { TaskMapping, TaskMappingProgress, IntegratedSubsection } from '@/types/progress';

interface TaskMappingState {
  mappings: TaskMapping[];
  mappingProgress: TaskMappingProgress[];
  isLoading: boolean;

  // Task Mapping Management
  createMapping: (mapping: Omit<TaskMapping, 'id' | 'createdAt'>) => string;
  updateMapping: (id: string, updates: Partial<TaskMapping>) => void;
  deleteMapping: (id: string) => void;
  getMappingsBySource: (sourceDocumentId: string, sourceSectionId?: string) => TaskMapping[];
  getMappingsByTarget: (targetDocumentId: string, targetSectionId?: string) => TaskMapping[];
  
  // Progress Synchronization
  syncProgress: (mappingId: string, sourceProgress: number) => void;
  updateTargetProgress: (mappingId: string, targetProgress: number) => void;
  getMappingProgress: (mappingId: string) => TaskMappingProgress | null;
  
  // Integration Helpers
  isSubsectionMapped: (documentId: string, sectionId: string, subsectionId: string) => boolean;
  getMappedTargets: (sourceDocumentId: string, sourceSectionId: string, sourceSubsectionId: string) => TaskMapping[];
  getMappedSources: (targetDocumentId: string, targetSectionId: string, targetSubsectionId?: string) => TaskMapping[];
  
  // Bulk Operations
  createBulkMappings: (mappings: Omit<TaskMapping, 'id' | 'createdAt'>[]) => string[];
  syncAllMappings: () => void;
  
  // Statistics
  getMappingStats: () => {
    totalMappings: number;
    activeMappings: number;
    syncedMappings: number;
    pendingMappings: number;
    errorMappings: number;
  };
}

export const useTaskMappingStore = create<TaskMappingState>()(
  persist(
    (set, get) => ({
      mappings: [],
      mappingProgress: [],
      isLoading: false,

      createMapping: (mappingData) => {
        const id = `mapping-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
        const now = new Date().toISOString();
        
        const newMapping: TaskMapping = {
          ...mappingData,
          id,
          createdAt: now,
        };

        // Create initial progress tracking
        const initialProgress: TaskMappingProgress = {
          mappingId: id,
          sourceProgress: 0,
          targetProgress: 0,
          lastSyncAt: now,
          syncStatus: 'synced'
        };

        set((state) => ({
          mappings: [...state.mappings, newMapping],
          mappingProgress: [...state.mappingProgress, initialProgress]
        }));

        return id;
      },

      updateMapping: (id, updates) => {
        set((state) => ({
          mappings: state.mappings.map(mapping =>
            mapping.id === id ? { ...mapping, ...updates } : mapping
          )
        }));
      },

      deleteMapping: (id) => {
        set((state) => ({
          mappings: state.mappings.filter(mapping => mapping.id !== id),
          mappingProgress: state.mappingProgress.filter(progress => progress.mappingId !== id)
        }));
      },

      getMappingsBySource: (sourceDocumentId, sourceSectionId) => {
        const { mappings } = get();
        return mappings.filter(mapping => 
          mapping.sourceDocumentId === sourceDocumentId &&
          (!sourceSectionId || mapping.sourceSectionId === sourceSectionId) &&
          mapping.isActive
        );
      },

      getMappingsByTarget: (targetDocumentId, targetSectionId) => {
        const { mappings } = get();
        return mappings.filter(mapping => 
          mapping.targetDocumentId === targetDocumentId &&
          (!targetSectionId || mapping.targetSectionId === targetSectionId) &&
          mapping.isActive
        );
      },

      syncProgress: (mappingId, sourceProgress) => {
        const now = new Date().toISOString();
        
        set((state) => ({
          mappingProgress: state.mappingProgress.map(progress =>
            progress.mappingId === mappingId
              ? {
                  ...progress,
                  sourceProgress,
                  lastSyncAt: now,
                  syncStatus: 'pending' as const
                }
              : progress
          )
        }));

        // Trigger target progress update
        const { updateTargetProgress } = get();
        updateTargetProgress(mappingId, sourceProgress);
      },

      updateTargetProgress: (mappingId, targetProgress) => {
        const now = new Date().toISOString();
        
        set((state) => ({
          mappingProgress: state.mappingProgress.map(progress =>
            progress.mappingId === mappingId
              ? {
                  ...progress,
                  targetProgress,
                  lastSyncAt: now,
                  syncStatus: 'synced' as const
                }
              : progress
          )
        }));
      },

      getMappingProgress: (mappingId) => {
        const { mappingProgress } = get();
        return mappingProgress.find(progress => progress.mappingId === mappingId) || null;
      },

      isSubsectionMapped: (documentId, sectionId, subsectionId) => {
        const { mappings } = get();
        return mappings.some(mapping =>
          ((mapping.sourceDocumentId === documentId && 
            mapping.sourceSectionId === sectionId && 
            mapping.sourceSubsectionId === subsectionId) ||
           (mapping.targetDocumentId === documentId && 
            mapping.targetSectionId === sectionId && 
            mapping.targetSubsectionId === subsectionId)) &&
          mapping.isActive
        );
      },

      getMappedTargets: (sourceDocumentId, sourceSectionId, sourceSubsectionId) => {
        const { mappings } = get();
        return mappings.filter(mapping =>
          mapping.sourceDocumentId === sourceDocumentId &&
          mapping.sourceSectionId === sourceSectionId &&
          mapping.sourceSubsectionId === sourceSubsectionId &&
          mapping.isActive
        );
      },

      getMappedSources: (targetDocumentId, targetSectionId, targetSubsectionId) => {
        const { mappings } = get();
        return mappings.filter(mapping =>
          mapping.targetDocumentId === targetDocumentId &&
          mapping.targetSectionId === targetSectionId &&
          (!targetSubsectionId || mapping.targetSubsectionId === targetSubsectionId) &&
          mapping.isActive
        );
      },

      createBulkMappings: (mappingsData) => {
        const ids: string[] = [];
        const now = new Date().toISOString();
        
        const newMappings: TaskMapping[] = mappingsData.map(mappingData => {
          const id = `mapping-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
          ids.push(id);
          
          return {
            ...mappingData,
            id,
            createdAt: now,
          };
        });

        const newProgress: TaskMappingProgress[] = newMappings.map(mapping => ({
          mappingId: mapping.id,
          sourceProgress: 0,
          targetProgress: 0,
          lastSyncAt: now,
          syncStatus: 'synced' as const
        }));

        set((state) => ({
          mappings: [...state.mappings, ...newMappings],
          mappingProgress: [...state.mappingProgress, ...newProgress]
        }));

        return ids;
      },

      syncAllMappings: () => {
        // This would typically trigger a background sync process
        // For now, we'll just update the sync status
        const now = new Date().toISOString();
        
        set((state) => ({
          mappingProgress: state.mappingProgress.map(progress => ({
            ...progress,
            lastSyncAt: now,
            syncStatus: 'synced' as const
          }))
        }));
      },

      getMappingStats: () => {
        const { mappings, mappingProgress } = get();
        
        const totalMappings = mappings.length;
        const activeMappings = mappings.filter(m => m.isActive).length;
        const syncedMappings = mappingProgress.filter(p => p.syncStatus === 'synced').length;
        const pendingMappings = mappingProgress.filter(p => p.syncStatus === 'pending').length;
        const errorMappings = mappingProgress.filter(p => p.syncStatus === 'error').length;

        return {
          totalMappings,
          activeMappings,
          syncedMappings,
          pendingMappings,
          errorMappings
        };
      },
    }),
    {
      name: 'task-mapping-storage',
      version: 1,
    }
  )
);
