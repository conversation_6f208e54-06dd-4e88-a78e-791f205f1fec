import React, { useState, useEffect } from 'react';
import { useAuth } from '@/store/auth';
import { UserRole } from '@/types';

interface AuthDebugPanelProps {
  isVisible?: boolean;
}

export function AuthDebugPanel({ isVisible = false }: AuthDebugPanelProps) {
  const { user, isAuthenticated, hasRole } = useAuth();
  const [debugInfo, setDebugInfo] = useState<any>({});
  const [isExpanded, setIsExpanded] = useState(false);

  useEffect(() => {
    // Collect debug information
    const collectDebugInfo = () => {
      const savedUser = localStorage.getItem('smap_user');
      const configUsers = localStorage.getItem('config-users');
      
      let parsedSavedUser = null;
      let parsedConfigUsers = null;
      
      try {
        parsedSavedUser = savedUser ? JSON.parse(savedUser) : null;
      } catch (error) {
        console.error('Error parsing saved user:', error);
      }
      
      try {
        parsedConfigUsers = configUsers ? JSON.parse(configUsers) : null;
      } catch (error) {
        console.error('Error parsing config users:', error);
      }

      const info = {
        currentUser: user,
        isAuthenticated,
        savedUserInStorage: parsedSavedUser,
        configUsersInStorage: parsedConfigUsers,
        hasRoles: {
          admin_super: hasRole(['admin_super']),
          admin_biasa: hasRole(['admin_biasa']),
          reviewer: hasRole(['reviewer']),
          scoopers: hasRole(['scoopers']),
          karyawan: hasRole(['karyawan'])
        },
        documentAccess: {
          'doc-1': user?.role ? ['admin_super', 'admin_biasa', 'scoopers'].includes(user.role) : false,
          'doc-2': user?.role ? ['admin_super', 'admin_biasa', 'scoopers'].includes(user.role) : false,
          'doc-3': user?.role ? ['admin_super', 'admin_biasa', 'scoopers'].includes(user.role) : false,
          'doc-4': user?.role ? ['admin_super', 'admin_biasa'].includes(user.role) : false,
        },
        timestamp: new Date().toISOString()
      };

      setDebugInfo(info);
    };

    collectDebugInfo();
    
    // Update every 2 seconds
    const interval = setInterval(collectDebugInfo, 2000);
    
    return () => clearInterval(interval);
  }, [user, isAuthenticated, hasRole]);

  if (!isVisible) {
    return null;
  }

  const getRoleColor = (role: UserRole) => {
    const colors = {
      admin_super: 'text-red-600 bg-red-50',
      admin_biasa: 'text-blue-600 bg-blue-50',
      reviewer: 'text-green-600 bg-green-50',
      scoopers: 'text-purple-600 bg-purple-50',
      karyawan: 'text-gray-600 bg-gray-50'
    };
    return colors[role] || 'text-gray-600 bg-gray-50';
  };

  return (
    <div className="fixed bottom-4 right-4 z-50">
      <div className="bg-white border border-gray-300 rounded-lg shadow-lg max-w-md">
        {/* Header */}
        <div 
          className="flex items-center justify-between p-3 bg-gray-50 border-b cursor-pointer"
          onClick={() => setIsExpanded(!isExpanded)}
        >
          <div className="flex items-center space-x-2">
            <div className="w-3 h-3 rounded-full bg-green-500"></div>
            <span className="font-medium text-sm">Auth Debug</span>
          </div>
          <button className="text-gray-500 hover:text-gray-700">
            {isExpanded ? '−' : '+'}
          </button>
        </div>

        {/* Content */}
        {isExpanded && (
          <div className="p-3 max-h-96 overflow-y-auto text-xs">
            {/* Current User */}
            <div className="mb-3">
              <h4 className="font-semibold text-gray-700 mb-1">Current User</h4>
              {user ? (
                <div className="space-y-1">
                  <div>ID: <span className="font-mono">{user.id}</span></div>
                  <div>Name: <span className="font-mono">{user.name}</span></div>
                  <div>
                    Role: <span className={`px-2 py-1 rounded text-xs font-medium ${getRoleColor(user.role)}`}>
                      {user.role}
                    </span>
                  </div>
                  <div>Authenticated: <span className={isAuthenticated ? 'text-green-600' : 'text-red-600'}>
                    {isAuthenticated ? 'Yes' : 'No'}
                  </span></div>
                </div>
              ) : (
                <div className="text-red-600">No user logged in</div>
              )}
            </div>

            {/* Role Permissions */}
            <div className="mb-3">
              <h4 className="font-semibold text-gray-700 mb-1">Role Permissions</h4>
              <div className="grid grid-cols-2 gap-1">
                {Object.entries(debugInfo.hasRoles || {}).map(([role, hasAccess]) => (
                  <div key={role} className="flex items-center space-x-1">
                    <div className={`w-2 h-2 rounded-full ${hasAccess ? 'bg-green-500' : 'bg-gray-300'}`}></div>
                    <span className="text-xs">{role}</span>
                  </div>
                ))}
              </div>
            </div>

            {/* Document Access */}
            <div className="mb-3">
              <h4 className="font-semibold text-gray-700 mb-1">Document Access</h4>
              <div className="space-y-1">
                {Object.entries(debugInfo.documentAccess || {}).map(([doc, canAccess]) => (
                  <div key={doc} className="flex items-center justify-between">
                    <span className="text-xs">{doc}</span>
                    <span className={`text-xs ${canAccess ? 'text-green-600' : 'text-red-600'}`}>
                      {canAccess ? '✓' : '✗'}
                    </span>
                  </div>
                ))}
              </div>
            </div>

            {/* Storage Info */}
            <div className="mb-3">
              <h4 className="font-semibold text-gray-700 mb-1">Storage Info</h4>
              <div className="space-y-1">
                <div>
                  Saved User: <span className={debugInfo.savedUserInStorage ? 'text-green-600' : 'text-red-600'}>
                    {debugInfo.savedUserInStorage ? 'Found' : 'Not found'}
                  </span>
                </div>
                <div>
                  Config Users: <span className={debugInfo.configUsersInStorage ? 'text-green-600' : 'text-red-600'}>
                    {debugInfo.configUsersInStorage ? 'Found' : 'Not found'}
                  </span>
                </div>
              </div>
            </div>

            {/* Actions */}
            <div className="border-t pt-2">
              <h4 className="font-semibold text-gray-700 mb-1">Quick Actions</h4>
              <div className="space-y-1">
                <button
                  onClick={() => {
                    localStorage.clear();
                    window.location.reload();
                  }}
                  className="w-full text-left text-xs text-red-600 hover:text-red-800 p-1 hover:bg-red-50 rounded"
                >
                  Clear Storage & Reload
                </button>
                <button
                  onClick={() => {
                    console.log('Debug Info:', debugInfo);
                  }}
                  className="w-full text-left text-xs text-blue-600 hover:text-blue-800 p-1 hover:bg-blue-50 rounded"
                >
                  Log Debug Info
                </button>
                <button
                  onClick={() => {
                    window.location.href = '/login';
                  }}
                  className="w-full text-left text-xs text-green-600 hover:text-green-800 p-1 hover:bg-green-50 rounded"
                >
                  Go to Login
                </button>
              </div>
            </div>

            {/* Timestamp */}
            <div className="text-xs text-gray-500 mt-2 pt-2 border-t">
              Last updated: {new Date(debugInfo.timestamp).toLocaleTimeString()}
            </div>
          </div>
        )}
      </div>
    </div>
  );
}

// Hook untuk mengaktifkan debug panel
export function useAuthDebug() {
  const [isDebugVisible, setIsDebugVisible] = useState(false);

  useEffect(() => {
    const handleKeyPress = (event: KeyboardEvent) => {
      // Ctrl + Shift + D untuk toggle debug panel
      if (event.ctrlKey && event.shiftKey && event.key === 'D') {
        setIsDebugVisible(prev => !prev);
      }
    };

    window.addEventListener('keydown', handleKeyPress);
    
    return () => {
      window.removeEventListener('keydown', handleKeyPress);
    };
  }, []);

  return { isDebugVisible, setIsDebugVisible };
}
