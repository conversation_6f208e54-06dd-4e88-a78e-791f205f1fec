'use client';

import { useAuth, getRoleDisplayName } from '@/store/auth';
import { useState } from 'react';
import { usePathname } from 'next/navigation';
import Image from 'next/image';
import { Bars3Icon, XMarkIcon, CalendarIcon } from '@heroicons/react/24/outline';
import { useProgram } from '@/contexts/ProgramContext';
import { AuthDebugPanel, useAuthDebug } from '@/components/debug/AuthDebugPanel';

interface AppLayoutProps {
  children: React.ReactNode;
}

function ProgramIndicator() {
  const { activeProgram } = useProgram();

  if (!activeProgram) return null;

  return (
    <div className="hidden md:flex items-center space-x-2 px-3 py-1 bg-blue-50 border border-blue-200 rounded-lg">
      <CalendarIcon className="h-4 w-4 text-blue-600" />
      <span className="text-sm font-medium text-blue-700">
        {activeProgram.name}
      </span>
    </div>
  );
}

export default function AppLayout({
  children
}: AppLayoutProps) {
  const { user, logout } = useAuth();
  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);
  const pathname = usePathname();
  const { isDebugVisible } = useAuthDebug();

  // Function to determine if a menu item is active
  const isActive = (path: string) => {
    if (path === '/dashboard') {
      return pathname === '/dashboard';
    }
    return pathname.startsWith(path);
  };

  // Function to get nav link classes
  const getNavLinkClasses = (path: string, isMobile: boolean = false) => {
    const baseClasses = "font-gotham-rounded font-medium transition-colors duration-200";
    const mobileClasses = isMobile ? "block px-3 py-2 rounded-md text-base" : "";

    if (isActive(path)) {
      // Active state classes
      const activeClasses = isMobile
        ? "text-primary bg-blue-50 border-l-4 border-primary"
        : "text-primary border-b-2 border-primary";
      return `${baseClasses} ${mobileClasses} ${activeClasses}`;
    } else {
      // Inactive state classes
      const inactiveClasses = isMobile
        ? "text-secondary hover:text-primary hover:bg-gray-50"
        : "text-secondary hover:text-primary";
      return `${baseClasses} ${mobileClasses} ${inactiveClasses}`;
    }
  };

  const toggleMobileMenu = () => {
    setIsMobileMenuOpen(!isMobileMenuOpen);
  };

  const closeMobileMenu = () => {
    setIsMobileMenuOpen(false);
  };

  if (!user) {
    // Redirect to login instead of showing loading
    if (typeof window !== 'undefined') {
      window.location.href = '/login';
    }
    return (
      <div className="min-h-screen flex items-center justify-center bg-gray-50">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-red-500 mx-auto"></div>
          <p className="mt-4 text-gray-600">Mengarahkan ke halaman login...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Fixed Navbar */}
      <nav className="fixed top-0 left-0 right-0 z-50 bg-white shadow-lg border-b border-gray-200">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between h-16">
            <div className="flex items-center space-x-8">
              <div className="flex items-center">
                <Image
                  src="/smap-logo.png"
                  alt="SMAP Logo"
                  width={120}
                  height={48}
                  className="h-12 w-auto transition-transform duration-200 hover:scale-105"
                  priority
                />
              </div>
              

              
              {/* Main Navigation */}
              <div className="hidden md:flex space-x-8">
                <a
                  href="/dashboard"
                  className="font-gotham-rounded font-medium text-secondary hover:text-primary transition-colors duration-200"
                >
                  Dashboard
                </a>

                {/* Admin Only Navigation - After Dashboard */}
                {(user.role === 'admin_super' || user.role === 'admin_biasa') && (
                  <>
                    <a
                      href="/config"
                      className="font-gotham-rounded font-medium text-secondary hover:text-primary transition-colors duration-200"
                    >
                      Config
                    </a>
                    <a
                      href="/manage-group"
                      className="font-gotham-rounded font-medium text-secondary hover:text-primary transition-colors duration-200"
                    >
                      Manage Group
                    </a>
                  </>
                )}

                {/* Super Admin Only Navigation */}
                {user.role === 'admin_super' && (
                  <a
                    href="/manage-program"
                    className="font-gotham-rounded font-medium text-secondary hover:text-primary transition-colors duration-200"
                  >
                    Manage Program
                  </a>
                )}


                <a
                  href="/repository"
                  className="font-gotham-rounded font-medium text-secondary hover:text-primary transition-colors duration-200"
                >
                  Risk Assessment
                </a>
              </div>
            </div>
            
            <div className="flex items-center space-x-4">
              {/* Program Indicator */}
              <ProgramIndicator />

              {/* Desktop User Info */}
              <div className="hidden md:flex items-center space-x-4">
                <span className="font-gotham text-sm text-secondary">
                  <span className="font-medium">{user.name}</span>
                  <span className="text-xs ml-1">({getRoleDisplayName(user.role)})</span>
                </span>
                <button
                  onClick={() => {
                    logout();
                    window.location.href = '/login';
                  }}
                  className="font-gotham-rounded font-medium text-sm text-primary hover:text-primary-700 transition-colors duration-200"
                >
                  Logout
                </button>
              </div>

              {/* Mobile menu button */}
              <div className="md:hidden">
                <button
                  onClick={toggleMobileMenu}
                  className="inline-flex items-center justify-center p-2 rounded-md text-gray-400 hover:text-gray-500 hover:bg-gray-100 focus:outline-none focus:ring-2 focus:ring-inset focus:ring-primary"
                  aria-expanded="false"
                >
                  <span className="sr-only">Open main menu</span>
                  {isMobileMenuOpen ? (
                    <XMarkIcon className="block h-6 w-6" aria-hidden="true" />
                  ) : (
                    <Bars3Icon className="block h-6 w-6" aria-hidden="true" />
                  )}
                </button>
              </div>
            </div>
          </div>
        </div>

        {/* Mobile menu */}
        <div className={`md:hidden ${isMobileMenuOpen ? 'block' : 'hidden'}`}>
          <div className="px-2 pt-2 pb-3 space-y-1 sm:px-3 bg-white border-t border-gray-200 shadow-lg">
            <a
              href="/dashboard"
              onClick={closeMobileMenu}
              className="font-gotham-rounded font-medium text-secondary hover:text-primary hover:bg-gray-50 block px-3 py-2 rounded-md text-base transition-colors duration-200"
            >
              Dashboard
            </a>

            {/* Admin Only Navigation - Mobile */}
            {(user.role === 'admin_super' || user.role === 'admin_biasa') && (
              <>
                <a
                  href="/config"
                  onClick={closeMobileMenu}
                  className="font-gotham-rounded font-medium text-secondary hover:text-primary hover:bg-gray-50 block px-3 py-2 rounded-md text-base transition-colors duration-200"
                >
                  Config
                </a>
                <a
                  href="/manage-group"
                  onClick={closeMobileMenu}
                  className="font-gotham-rounded font-medium text-secondary hover:text-primary hover:bg-gray-50 block px-3 py-2 rounded-md text-base transition-colors duration-200"
                >
                  Manage Group
                </a>
              </>
            )}

            {/* Super Admin Only Navigation - Mobile */}
            {user.role === 'admin_super' && (
              <a
                href="/manage-program"
                onClick={closeMobileMenu}
                className="font-gotham-rounded font-medium text-secondary hover:text-primary hover:bg-gray-50 block px-3 py-2 rounded-md text-base transition-colors duration-200"
              >
                Manage Program
              </a>
            )}


            <a
              href="/repository"
              onClick={closeMobileMenu}
              className="font-gotham-rounded font-medium text-secondary hover:text-primary hover:bg-gray-50 block px-3 py-2 rounded-md text-base transition-colors duration-200"
            >
              Risk Assessment
            </a>

            {/* Mobile User Info and Logout */}
            <div className="border-t border-gray-200 pt-4 pb-3">
              <div className="px-3">
                <div className="text-base font-medium text-gray-800">{user.name}</div>
                <div className="text-sm text-gray-500">({getRoleDisplayName(user.role)})</div>
              </div>
              <div className="mt-3 px-3">
                <button
                  onClick={() => {
                    logout();
                    window.location.href = '/login';
                  }}
                  className="font-gotham-rounded font-medium text-sm text-primary hover:text-primary-700 transition-colors duration-200 w-full text-left"
                >
                  Logout
                </button>
              </div>
            </div>
          </div>
        </div>
      </nav>

      {/* Main Content with proper spacing from fixed navbar */}
      <main className="pt-24 pb-8">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          {children}
        </div>
      </main>

      {/* Debug Panel - Press Ctrl+Shift+D to toggle */}
      <AuthDebugPanel isVisible={isDebugVisible} />
    </div>
  );
}
