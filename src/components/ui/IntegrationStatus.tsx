import React from 'react';
import { useIntegrationStatus } from '@/hooks/useTaskIntegration';
import { 
  LinkIcon, 
  CheckCircleIcon, 
  ClockIcon, 
  ExclamationTriangleIcon,
  ArrowPathIcon
} from '@heroicons/react/24/outline';

interface IntegrationStatusProps {
  documentId: string;
  sectionId: string;
  className?: string;
}

export function IntegrationStatus({ documentId, sectionId, className = '' }: IntegrationStatusProps) {
  const status = useIntegrationStatus(documentId, sectionId);

  if (!status.isMapped) {
    return null;
  }

  const getStatusIcon = () => {
    if (status.pendingCount > 0) {
      return <ClockIcon className="h-4 w-4 text-yellow-500" />;
    }
    if (status.syncedCount === status.mappingCount) {
      return <CheckCircleIcon className="h-4 w-4 text-green-500" />;
    }
    return <ExclamationTriangleIcon className="h-4 w-4 text-red-500" />;
  };

  const getStatusText = () => {
    if (status.pendingCount > 0) {
      return `${status.pendingCount} pending sync`;
    }
    if (status.syncedCount === status.mappingCount) {
      return 'All synced';
    }
    return 'Sync issues';
  };

  const getStatusColor = () => {
    if (status.pendingCount > 0) {
      return 'bg-yellow-50 border-yellow-200 text-yellow-700';
    }
    if (status.syncedCount === status.mappingCount) {
      return 'bg-green-50 border-green-200 text-green-700';
    }
    return 'bg-red-50 border-red-200 text-red-700';
  };

  return (
    <div className={`inline-flex items-center space-x-2 px-2 py-1 rounded-lg border text-xs font-medium ${getStatusColor()} ${className}`}>
      <LinkIcon className="h-3 w-3" />
      {getStatusIcon()}
      <span>{getStatusText()}</span>
      <span className="text-gray-500">({status.mappingCount})</span>
    </div>
  );
}

interface IntegrationBadgeProps {
  isIntegrated: boolean;
  targetTitle?: string;
  className?: string;
}

export function IntegrationBadge({ isIntegrated, targetTitle, className = '' }: IntegrationBadgeProps) {
  if (!isIntegrated) {
    return null;
  }

  return (
    <div className={`inline-flex items-center space-x-1 px-2 py-1 bg-blue-50 border border-blue-200 rounded-lg text-xs font-medium text-blue-700 ${className}`}>
      <LinkIcon className="h-3 w-3" />
      <span>→ {targetTitle || 'Evidence Mapping'}</span>
    </div>
  );
}

interface SyncProgressIndicatorProps {
  progress: number;
  isLoading?: boolean;
  className?: string;
}

export function SyncProgressIndicator({ progress, isLoading = false, className = '' }: SyncProgressIndicatorProps) {
  return (
    <div className={`flex items-center space-x-2 ${className}`}>
      {isLoading && (
        <ArrowPathIcon className="h-4 w-4 text-blue-500 animate-spin" />
      )}
      <div className="flex-1 bg-gray-200 rounded-full h-2">
        <div 
          className="bg-blue-500 h-2 rounded-full transition-all duration-300"
          style={{ width: `${Math.min(progress, 100)}%` }}
        />
      </div>
      <span className="text-xs font-medium text-gray-600">
        {Math.round(progress)}%
      </span>
    </div>
  );
}

interface IntegrationSummaryProps {
  documentId: string;
  className?: string;
}

export function IntegrationSummary({ documentId, className = '' }: IntegrationSummaryProps) {
  // This would typically fetch integration summary data
  // For now, we'll show a placeholder
  
  return (
    <div className={`bg-blue-50 border border-blue-200 rounded-lg p-4 ${className}`}>
      <div className="flex items-center space-x-2 mb-2">
        <LinkIcon className="h-5 w-5 text-blue-600" />
        <h3 className="font-medium text-blue-900">Integration Status</h3>
      </div>
      <p className="text-sm text-blue-700">
        This section is integrated with Evidence Mapping Klausul. 
        Progress will be automatically synchronized.
      </p>
    </div>
  );
}

// Hook for real-time sync status
export function useSyncStatus(mappingId: string) {
  const [syncStatus, setSyncStatus] = React.useState<{
    isLoading: boolean;
    lastSync: string | null;
    error: string | null;
  }>({
    isLoading: false,
    lastSync: null,
    error: null
  });

  React.useEffect(() => {
    const handleSyncStart = (event: CustomEvent) => {
      if (event.detail.mappingId === mappingId) {
        setSyncStatus(prev => ({ ...prev, isLoading: true, error: null }));
      }
    };

    const handleSyncComplete = (event: CustomEvent) => {
      if (event.detail.mappingId === mappingId) {
        setSyncStatus(prev => ({
          ...prev,
          isLoading: false,
          lastSync: new Date().toISOString(),
          error: null
        }));
      }
    };

    const handleSyncError = (event: CustomEvent) => {
      if (event.detail.mappingId === mappingId) {
        setSyncStatus(prev => ({
          ...prev,
          isLoading: false,
          error: event.detail.error
        }));
      }
    };

    window.addEventListener('syncStart', handleSyncStart as EventListener);
    window.addEventListener('syncComplete', handleSyncComplete as EventListener);
    window.addEventListener('syncError', handleSyncError as EventListener);

    return () => {
      window.removeEventListener('syncStart', handleSyncStart as EventListener);
      window.removeEventListener('syncComplete', handleSyncComplete as EventListener);
      window.removeEventListener('syncError', handleSyncError as EventListener);
    };
  }, [mappingId]);

  return syncStatus;
}

// Component for displaying sync history
interface SyncHistoryProps {
  mappingId: string;
  className?: string;
}

export function SyncHistory({ mappingId, className = '' }: SyncHistoryProps) {
  const [history, setHistory] = React.useState<Array<{
    timestamp: string;
    status: 'success' | 'error';
    message: string;
  }>>([]);

  React.useEffect(() => {
    // Load sync history from localStorage
    const savedHistory = localStorage.getItem(`sync-history-${mappingId}`);
    if (savedHistory) {
      setHistory(JSON.parse(savedHistory));
    }

    const handleSyncEvent = (event: CustomEvent) => {
      if (event.detail.mappingId === mappingId) {
        const newEntry = {
          timestamp: new Date().toISOString(),
          status: event.type === 'syncComplete' ? 'success' as const : 'error' as const,
          message: event.detail.message || (event.type === 'syncComplete' ? 'Sync completed' : 'Sync failed')
        };

        setHistory(prev => {
          const updated = [newEntry, ...prev].slice(0, 10); // Keep last 10 entries
          localStorage.setItem(`sync-history-${mappingId}`, JSON.stringify(updated));
          return updated;
        });
      }
    };

    window.addEventListener('syncComplete', handleSyncEvent as EventListener);
    window.addEventListener('syncError', handleSyncEvent as EventListener);

    return () => {
      window.removeEventListener('syncComplete', handleSyncEvent as EventListener);
      window.removeEventListener('syncError', handleSyncEvent as EventListener);
    };
  }, [mappingId]);

  if (history.length === 0) {
    return null;
  }

  return (
    <div className={`bg-gray-50 rounded-lg p-3 ${className}`}>
      <h4 className="text-sm font-medium text-gray-700 mb-2">Sync History</h4>
      <div className="space-y-1">
        {history.slice(0, 3).map((entry, index) => (
          <div key={index} className="flex items-center space-x-2 text-xs">
            {entry.status === 'success' ? (
              <CheckCircleIcon className="h-3 w-3 text-green-500" />
            ) : (
              <ExclamationTriangleIcon className="h-3 w-3 text-red-500" />
            )}
            <span className="text-gray-600">
              {new Date(entry.timestamp).toLocaleTimeString()}
            </span>
            <span className="text-gray-500">{entry.message}</span>
          </div>
        ))}
      </div>
    </div>
  );
}
