'use client';

import { useState, useEffect } from 'react';
import { useAuth } from '@/store/auth';
import { useProgramDocument, useProgramRiskAssessments } from '@/contexts/ProgramContext';
import { useTaskMappingStore } from '@/store/taskMapping';
import AppLayout from '@/components/layout/AppLayout';
import { IntegrationStatus, IntegrationSummary } from '@/components/ui/IntegrationStatus';
import {
  DocumentTextIcon,
  ChevronDownIcon,
  ChevronUpIcon,
  UserGroupIcon,
  CloudArrowUpIcon,
  EyeIcon,
  ArrowLeftIcon,
  XMarkIcon,
  DocumentIcon,
  TrashIcon,
  ArrowDownTrayIcon,
  ArrowRightIcon,
  PlusIcon
} from '@heroicons/react/24/outline';

// Sub-documents for Evidence Mapping Klausul
const SUB_DOCUMENTS: any[] = [];

// Mock files data
const MOCK_FILES = [
  {
    id: '1',
    name: 'Evidence_Mapping_Klausul_4.pdf',
    type: 'pdf',
    size: '2.8 MB',
    uploadDate: '2024-01-22',
    uploadedBy: 'Admin Super'
  },
  {
    id: '2',
    name: 'Recruitment_Evidence_Matrix.xlsx',
    type: 'xlsx',
    size: '1.2 MB',
    uploadDate: '2024-01-21',
    uploadedBy: 'Scoopers'
  }
];

// File Manager Modal Component
interface FileManagerModalProps {
  isOpen: boolean;
  onClose: () => void;
  sectionId: string;
  sectionTitle: string;
}

function FileManagerModal({ isOpen, onClose, sectionId, sectionTitle }: FileManagerModalProps) {
  const [files, setFiles] = useState(MOCK_FILES);
  const [selectedFile, setSelectedFile] = useState<File | null>(null);
  const [isUploading, setIsUploading] = useState(false);

  if (!isOpen) return null;

  const handleFileSelect = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (file) {
      setSelectedFile(file);
    }
  };

  const handleUpload = async () => {
    if (!selectedFile) return;

    setIsUploading(true);
    
    // Simulate upload delay
    await new Promise(resolve => setTimeout(resolve, 2000));
    
    // Add new file to the list
    const newFile = {
      id: (files.length + 1).toString(),
      name: selectedFile.name,
      type: selectedFile.name.split('.').pop() || 'unknown',
      size: `${(selectedFile.size / (1024 * 1024)).toFixed(1)} MB`,
      uploadDate: new Date().toISOString().split('T')[0],
      uploadedBy: 'Current User'
    };
    
    setFiles([...files, newFile]);
    setSelectedFile(null);
    setIsUploading(false);
  };

  const handleDeleteFile = (fileId: string) => {
    setFiles(files.filter(file => file.id !== fileId));
  };

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white rounded-xl shadow-xl max-w-4xl w-full mx-4 max-h-[90vh] overflow-hidden">
        <div className="p-6 border-b border-gray-200">
          <div className="flex items-center justify-between">
            <h2 className="font-gotham-rounded text-xl font-bold text-gray-900">
              File Manager - {sectionTitle}
            </h2>
            <button
              onClick={onClose}
              className="text-gray-400 hover:text-gray-600 transition-colors"
            >
              <XMarkIcon className="h-6 w-6" />
            </button>
          </div>
        </div>
        
        <div className="p-6">
          {/* Upload Section */}
          <div className="mb-6 p-4 border-2 border-dashed border-gray-300 rounded-lg">
            <div className="text-center">
              <CloudArrowUpIcon className="mx-auto h-12 w-12 text-gray-400 mb-4" />
              <div className="flex flex-col items-center space-y-2">
                <input
                  type="file"
                  onChange={handleFileSelect}
                  className="hidden"
                  id="file-upload"
                  accept=".pdf,.doc,.docx,.xls,.xlsx,.ppt,.pptx"
                />
                <label
                  htmlFor="file-upload"
                  className="cursor-pointer px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors font-gotham font-medium"
                >
                  Pilih File
                </label>
                {selectedFile && (
                  <div className="flex items-center space-x-2">
                    <span className="font-gotham text-sm text-gray-600">
                      {selectedFile.name} ({(selectedFile.size / (1024 * 1024)).toFixed(1)} MB)
                    </span>
                    <button
                      onClick={handleUpload}
                      disabled={isUploading}
                      className="px-3 py-1 bg-green-600 text-white text-sm rounded hover:bg-green-700 transition-colors disabled:opacity-50 font-gotham"
                    >
                      {isUploading ? 'Uploading...' : 'Upload'}
                    </button>
                  </div>
                )}
              </div>
              <p className="font-gotham text-xs text-gray-500 mt-2">
                Supported formats: PDF, DOC, DOCX, XLS, XLSX, PPT, PPTX (Max 10MB)
              </p>
            </div>
          </div>

          {/* Files List */}
          <div>
            <h3 className="font-gotham-rounded text-lg font-semibold text-gray-900 mb-4">
              File yang Tersedia
            </h3>

            <div className="space-y-2">
              {files.map((file) => (
                <div key={file.id} className="bg-white border border-gray-200 rounded-lg p-4 hover:bg-gray-50 transition-colors">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center space-x-3">
                      <DocumentIcon className="h-6 w-6 text-blue-600" />
                      <div>
                        <h4 className="font-gotham font-medium text-gray-900 text-sm">{file.name}</h4>
                        <p className="font-gotham text-xs text-gray-500">
                          {file.size} • Uploaded {file.uploadDate} by {file.uploadedBy}
                        </p>
                      </div>
                    </div>
                    <div className="flex items-center space-x-2">
                      <button className="p-2 text-blue-600 hover:bg-blue-50 rounded-lg transition-colors">
                        <EyeIcon className="h-4 w-4" />
                      </button>
                      <button className="p-2 text-green-600 hover:bg-green-50 rounded-lg transition-colors">
                        <ArrowDownTrayIcon className="h-4 w-4" />
                      </button>
                      <button 
                        onClick={() => handleDeleteFile(file.id)}
                        className="p-2 text-red-600 hover:bg-red-50 rounded-lg transition-colors"
                      >
                        <TrashIcon className="h-4 w-4" />
                      </button>
                    </div>
                  </div>
                </div>
              ))}
            </div>

            {files.length === 0 && (
              <div className="text-center py-8">
                <DocumentIcon className="mx-auto h-12 w-12 text-gray-400 mb-4" />
                <p className="font-gotham text-gray-500">Belum ada file yang diupload</p>
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  );
}

// Add Section Modal Component
interface AddSectionModalProps {
  isOpen: boolean;
  onClose: () => void;
  onAdd: (title: string, description: string) => void;
}

function AddSectionModal({ isOpen, onClose, onAdd }: AddSectionModalProps) {
  const [title, setTitle] = useState('');
  const [description, setDescription] = useState('');

  if (!isOpen) return null;

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    if (title.trim()) {
      onAdd(title.trim(), description.trim());
      setTitle('');
      setDescription('');
      onClose();
    }
  };

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div className="bg-white rounded-xl shadow-2xl w-full max-w-md">
        <div className="p-6 border-b border-gray-200">
          <div className="flex items-center justify-between">
            <h2 className="text-xl font-bold text-gray-900">Tambah Section</h2>
            <button
              onClick={onClose}
              className="text-gray-400 hover:text-gray-600 text-2xl"
            >
              ×
            </button>
          </div>
        </div>

        <form onSubmit={handleSubmit} className="p-6">
          <div className="space-y-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Judul Section
              </label>
              <input
                type="text"
                value={title}
                onChange={(e) => setTitle(e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                placeholder="Masukkan judul section"
                required
              />
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Deskripsi <span className="text-gray-400 font-normal">(opsional)</span>
              </label>
              <textarea
                value={description}
                onChange={(e) => setDescription(e.target.value)}
                rows={3}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                placeholder="Masukkan deskripsi section (opsional)"
              />
            </div>
          </div>

          <div className="flex space-x-3 mt-6">
            <button
              type="button"
              onClick={onClose}
              className="flex-1 px-4 py-2 text-gray-700 bg-gray-100 rounded-lg hover:bg-gray-200 transition-colors font-gotham font-medium"
            >
              Batal
            </button>
            <button
              type="submit"
              disabled={!title.trim()}
              className="flex-1 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:bg-gray-300 disabled:cursor-not-allowed transition-colors font-gotham font-medium"
            >
              Tambah Section
            </button>
          </div>
        </form>
      </div>
    </div>
  );
}

// Add Sub-Section Modal Component
interface AddSubSectionModalProps {
  isOpen: boolean;
  onClose: () => void;
  onAdd: (title: string, description: string, type: 'upload' | 'form', formUrl?: string) => void;
  parentSectionTitle: string;
}

function AddSubSectionModal({ isOpen, onClose, onAdd, parentSectionTitle }: AddSubSectionModalProps) {
  const [title, setTitle] = useState('');
  const [description, setDescription] = useState('');
  const [type, setType] = useState<'upload' | 'form'>('upload');
  const [formUrl, setFormUrl] = useState('');

  if (!isOpen) return null;

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    if (title.trim() && (type === 'upload' || (type === 'form' && formUrl.trim()))) {
      onAdd(title.trim(), description.trim(), type, type === 'form' ? formUrl.trim() : undefined);
      setTitle('');
      setDescription('');
      setType('upload');
      setFormUrl('');
      onClose();
    }
  };

  const handleClose = () => {
    setTitle('');
    setDescription('');
    setType('upload');
    setFormUrl('');
    onClose();
  };

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div className="bg-white rounded-xl shadow-2xl w-full max-w-md">
        <div className="p-6 border-b border-gray-200">
          <div className="flex items-center justify-between">
            <h2 className="text-xl font-bold text-gray-900">Tambah Sub-Section</h2>
            <button
              onClick={onClose}
              className="text-gray-400 hover:text-gray-600 text-2xl"
            >
              ×
            </button>
          </div>
          <p className="text-sm text-gray-600 mt-2">
            Untuk section: <span className="font-medium">{parentSectionTitle}</span>
          </p>
        </div>

        <form onSubmit={handleSubmit} className="p-6">
          <div className="space-y-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Judul Sub-Section
              </label>
              <input
                type="text"
                value={title}
                onChange={(e) => setTitle(e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                placeholder="Masukkan judul sub-section"
                required
              />
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Deskripsi <span className="text-gray-400 font-normal">(opsional)</span>
              </label>
              <textarea
                value={description}
                onChange={(e) => setDescription(e.target.value)}
                rows={3}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                placeholder="Masukkan deskripsi sub-section (opsional)"
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Tipe Sub-Section
              </label>
              <div className="space-y-3">
                <label className="flex items-center p-3 border border-gray-200 rounded-lg hover:bg-gray-50 cursor-pointer">
                  <input
                    type="radio"
                    name="subsectionType"
                    value="upload"
                    checked={type === 'upload'}
                    onChange={(e) => setType(e.target.value as 'upload' | 'form')}
                    className="mr-3"
                  />
                  <div className="flex items-center space-x-3">
                    <div className="p-2 bg-blue-100 text-blue-600 rounded-lg">
                      <DocumentTextIcon className="h-5 w-5" />
                    </div>
                    <div>
                      <div className="font-medium text-gray-900">Upload Dokumen</div>
                      <div className="text-sm text-gray-600">Sub-section untuk upload file dokumen</div>
                    </div>
                  </div>
                </label>

                <label className="flex items-center p-3 border border-gray-200 rounded-lg hover:bg-gray-50 cursor-pointer">
                  <input
                    type="radio"
                    name="subsectionType"
                    value="form"
                    checked={type === 'form'}
                    onChange={(e) => setType(e.target.value as 'upload' | 'form')}
                    className="mr-3"
                  />
                  <div className="flex items-center space-x-3">
                    <div className="p-2 bg-green-100 text-green-600 rounded-lg">
                      <DocumentIcon className="h-5 w-5" />
                    </div>
                    <div>
                      <div className="font-medium text-gray-900">Isi Formulir</div>
                      <div className="text-sm text-gray-600">Sub-section untuk mengisi formulir online</div>
                    </div>
                  </div>
                </label>
              </div>
            </div>

            {type === 'form' && (
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  URL Formulir
                </label>
                <input
                  type="url"
                  value={formUrl}
                  onChange={(e) => setFormUrl(e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                  placeholder="Contoh: /forms/risk-assessment"
                  required
                />
                <p className="text-xs text-gray-500 mt-1">
                  Masukkan URL relatif atau absolut untuk formulir
                </p>
              </div>
            )}
          </div>

          <div className="flex space-x-3 mt-6">
            <button
              type="button"
              onClick={handleClose}
              className="flex-1 px-4 py-2 text-gray-700 bg-gray-100 rounded-lg hover:bg-gray-200 transition-colors font-gotham font-medium"
            >
              Batal
            </button>
            <button
              type="submit"
              disabled={!title.trim() || (type === 'form' && !formUrl.trim())}
              className="flex-1 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:bg-gray-300 disabled:cursor-not-allowed transition-colors font-gotham font-medium"
            >
              Tambah Sub-Section
            </button>
          </div>
        </form>
      </div>
    </div>
  );
}

export default function EvidenceMappingPage() {
  const { user } = useAuth();
  const {
    dynamicSections: dynamicMainSections,
    dynamicSubSections,
    updateDynamicSections: updateDynamicMainSections,
    updateDynamicSubSections
  } = useProgramDocument('doc3');
  const { riskAssessments, updateRiskAssessments } = useProgramRiskAssessments();

  const [expandedSection, setExpandedSection] = useState<string | null>(null);
  const [expandedSubSection, setExpandedSubSection] = useState<string | null>(null);
  const [showFileManager, setShowFileManager] = useState(false);
  const [currentSection, setCurrentSection] = useState<string>('');
  const [hasSubmittedForm, setHasSubmittedForm] = useState(false);
  const [showAddSection, setShowAddSection] = useState(false);
  const [showAddSubSection, setShowAddSubSection] = useState(false);
  const [currentSubDocId, setCurrentSubDocId] = useState<string>('');

  // Check if THIS specific user has submitted risk assessment form
  useEffect(() => {
    if (user && user.role === 'scoopers') {
      try {
        const savedAssessments = JSON.parse(localStorage.getItem('risk-assessments') || '[]');
        // Check specifically for this user's submission by exact username match
        const userSubmission = savedAssessments.find((assessment: any) =>
          assessment.submittedBy === user.name ||
          assessment.dibuatOleh?.nama === user.name
        );
        setHasSubmittedForm(!!userSubmission);
        console.log(`Checking form submission for user: ${user.name}, found: ${!!userSubmission}`);
      } catch (error) {
        console.error('Error checking form submission:', error);
        setHasSubmittedForm(false);
      }
    } else {
      // Non-scoopers always show regular button
      setHasSubmittedForm(false);
    }
  }, [user]);

  if (!user) return null;





  const handleSectionToggle = (sectionId: string) => {
    setExpandedSection(expandedSection === sectionId ? null : sectionId);
  };

  const handleSubSectionToggle = (subSectionId: string) => {
    setExpandedSubSection(expandedSubSection === subSectionId ? null : subSectionId);
  };

  const handleNavigateToForm = (formUrl: string) => {
    // Navigate to form page
    window.location.href = formUrl;
  };

  const hasFileInSection = (sectionId: string) => {
    const savedFiles = localStorage.getItem('doc-3-section-files');
    const sectionFiles = savedFiles ? JSON.parse(savedFiles) : {};
    const filesForSection = sectionFiles[sectionId] || [];
    return filesForSection.length > 0;
  };

  const handleUpload = (subSectionId: string) => {
    setCurrentSection(subSectionId);
    setShowFileManager(true);
  };

  const handleViewFile = (subSectionId: string) => {
    // Handle view file
    console.log('View file for:', subSectionId);
    alert(`View file untuk ${subSectionId}`);
  };

  const handleBack = () => {
    window.history.back();
  };

  const getSectionTitle = (sectionId: string) => {
    const sectionTitles: Record<string, string> = {
      'klausul-4': 'Klausul 4 - Konteks Organisasi',
      'klausul-5': 'Klausul 5 - Kepemimpinan',
      'klausul-6': 'Klausul 6 - Perencanaan'
    };
    return sectionTitles[sectionId] || sectionId;
  };

  const handleAddSection = (title: string, description: string) => {
    // In a real app, this would make an API call
    console.log('Adding new section:', { title, description });
    alert(`Section "${title}" berhasil ditambahkan!`);
  };

  const handleAddSubSection = (title: string, description: string, type: 'upload' | 'form', formUrl?: string) => {
    // In a real app, this would make an API call
    console.log('Adding new sub-section:', { title, description, type, formUrl, parentId: currentSubDocId });
    alert(`Sub-section "${title}" (${type === 'form' ? 'Form' : 'Upload'}) berhasil ditambahkan!`);
  };

  const handleOpenAddSection = () => {
    setShowAddSection(true);
  };

  const handleOpenAddSubSection = (subDocId: string, subDocTitle: string) => {
    setCurrentSubDocId(subDocId);
    setShowAddSubSection(true);
  };

  return (
    <AppLayout>
      <div className="container mx-auto px-4 py-8">
        <div className="max-w-6xl mx-auto">
          {/* Header */}
          <div className="mb-8">
            <div className="flex items-center space-x-3 mb-4">
              <div className="p-3 bg-red-100 rounded-xl">
                <DocumentTextIcon className="h-8 w-8 text-red-600" />
              </div>
              <div>
                <h1 className="font-gotham-rounded text-3xl font-bold text-gray-900">
                  Evidence Mapping Klausul
                </h1>
                <p className="font-gotham text-gray-600 mt-1">
                  Pemetaan evidence untuk setiap klausul (khusus Scoopers)
                </p>
              </div>
            </div>
            
            {/* Breadcrumb */}
            <nav className="flex items-center space-x-2 text-sm text-gray-500 font-gotham mb-4">
              <a href="/dashboard" className="hover:text-red-600 transition-colors">
                Dashboard
              </a>
              <span>›</span>
              <span className="text-gray-900">Evidence Mapping Klausul</span>
            </nav>



            {/* Back Button */}
            <button
              onClick={handleBack}
              className="flex items-center space-x-2 px-4 py-2 bg-gray-100 text-gray-700 rounded-lg hover:bg-gray-200 transition-colors font-gotham font-medium"
            >
              <ArrowLeftIcon className="h-4 w-4" />
              <span>Kembali</span>
            </button>
          </div>

          {/* Sub-Documents List */}
          <div className="space-y-6">
            {SUB_DOCUMENTS.map((subDoc, index) => {
              const IconComponent = subDoc.icon;
              const isExpanded = expandedSection === subDoc.id;

              return (
                <div
                  key={subDoc.id}
                  className="border border-gray-200 rounded-xl"
                >
                  {/* Main Section Header */}
                  <div
                    className={`p-6 cursor-pointer hover:bg-gray-50 transition-all duration-200 ${subDoc.hoverColor}`}
                    onClick={() => handleSectionToggle(subDoc.id)}
                  >
                    <div className="flex items-start justify-between">
                      <div className="flex items-start space-x-4">
                        <div className={`p-3 rounded-xl ${subDoc.color}`}>
                          <IconComponent className="h-6 w-6" />
                        </div>
                        <div className="flex-1">
                          <div className="flex items-center space-x-2 mb-2">
                            <h3 className="font-gotham-rounded text-lg font-semibold text-gray-900">
                              {String.fromCharCode(65 + index)}. {subDoc.title}
                            </h3>
                            <IntegrationStatus
                              documentId="doc-3"
                              sectionId={subDoc.id}
                            />
                          </div>
                          <p className="font-gotham text-gray-600 text-sm leading-relaxed">
                            {subDoc.description}
                          </p>
                        </div>
                      </div>
                      <div className={`p-2 border border-gray-300 rounded-lg transition-all duration-200 ${subDoc.hoverColor}`}>
                        {isExpanded ? (
                          <ChevronUpIcon className={`h-5 w-5 text-gray-600 transition-colors duration-200 ${subDoc.iconHoverColor}`} />
                        ) : (
                          <ChevronDownIcon className={`h-5 w-5 text-gray-600 transition-colors duration-200 ${subDoc.iconHoverColor}`} />
                        )}
                      </div>
                    </div>
                  </div>

                  {/* Expanded Content - Show Upload Section */}
                  {isExpanded && (
                    <div className="border-t border-gray-200 bg-gray-50 p-6">
                      {/* Integration Summary */}
                      <IntegrationSummary
                        documentId="doc-3"
                        className="mb-4"
                      />

                      <div className="bg-white border border-gray-200 rounded-lg p-4">
                        <div className="flex items-center justify-between">
                          <div className="flex-1">
                            <h4 className="font-gotham-rounded text-base font-semibold text-gray-900 mb-1">
                              {String.fromCharCode(65 + index)}.1 Upload Dokumen {subDoc.title}
                            </h4>
                            <p className="font-gotham text-gray-600 text-sm">
                              Upload file dokumen untuk {subDoc.title}
                            </p>
                          </div>
                          <div className="flex items-center space-x-2 ml-4">
                            {/* Upload button - only for admin, scoopers */}
                            {(user.role === 'admin_super' || user.role === 'admin_biasa' || user.role === 'scoopers') && (
                              <button
                                onClick={() => handleUpload(subDoc.id)}
                                className="flex items-center space-x-2 px-3 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors text-sm font-gotham font-medium"
                              >
                                <CloudArrowUpIcon className="h-4 w-4" />
                                <span>Upload</span>
                              </button>
                            )}

                            {/* View File button - for all roles */}
                            <button
                              onClick={() => handleViewFile(subDoc.id)}
                              className={`flex items-center space-x-2 px-3 py-2 rounded-lg transition-colors text-sm font-gotham font-medium ${
                                hasFileInSection(subDoc.id)
                                  ? 'bg-green-600 text-white hover:bg-green-700'
                                  : 'bg-gray-600 text-white hover:bg-gray-700'
                              }`}
                            >
                              <EyeIcon className="h-4 w-4" />
                              <span>{hasFileInSection(subDoc.id) ? 'Lihat File' : 'Belum Ada File'}</span>
                            </button>
                          </div>
                        </div>
                      </div>
                    </div>
                  )}

                </div>
              );
            })}
          </div>

          {/* Add Section Button - only for Super Admin and Admin */}
          {(user.role === 'admin_super' || user.role === 'admin_biasa') && (
            <div className="border-2 border-dashed border-gray-300 rounded-xl p-6 text-center hover:border-blue-400 hover:bg-blue-50 transition-all duration-200">
              <button
                onClick={handleOpenAddSection}
                className="flex items-center justify-center space-x-2 mx-auto px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors font-gotham font-medium"
              >
                <PlusIcon className="h-5 w-5" />
                <span>Tambah Section Baru</span>
              </button>
              <p className="font-gotham text-sm text-gray-500 mt-2">
                Tambahkan section baru untuk Evidence Mapping Klausul
              </p>
            </div>
          )}

          {/* Info Section */}
          <div className="mt-12 bg-blue-50 rounded-xl p-6">
            <div className="flex items-start space-x-3">
              <div className="p-2 bg-blue-100 rounded-lg">
                <DocumentTextIcon className="h-5 w-5 text-blue-600" />
              </div>
              <div>
                <h3 className="font-gotham-rounded text-lg font-semibold text-blue-900 mb-2">
                  Informasi Evidence Mapping
                </h3>
                <p className="font-gotham text-blue-800 text-sm leading-relaxed">
                  Evidence Mapping Klausul khusus untuk tim Scoopers dalam melakukan pemetaan bukti
                  untuk setiap klausul ISO 37001. Dokumen ini mencakup evidence mapping untuk
                  proses recruitment dengan fokus pada klausul konteks organisasi, kepemimpinan,
                  dan perencanaan.
                </p>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* File Manager Modal */}
      <FileManagerModal
        isOpen={showFileManager}
        onClose={() => setShowFileManager(false)}
        sectionId={currentSection}
        sectionTitle={getSectionTitle(currentSection)}
      />

      {/* Add Section Modal */}
      <AddSectionModal
        isOpen={showAddSection}
        onClose={() => setShowAddSection(false)}
        onAdd={handleAddSection}
      />

      {/* Add Sub-Section Modal */}
      <AddSubSectionModal
        isOpen={showAddSubSection}
        onClose={() => setShowAddSubSection(false)}
        onAdd={handleAddSubSection}
        parentSectionTitle={SUB_DOCUMENTS.find(doc => doc.id === currentSubDocId)?.title || ''}
      />
    </AppLayout>
  );
}
