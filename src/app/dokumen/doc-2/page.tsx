'use client';

import { useState, useEffect } from 'react';
import { useAuth } from '@/store/auth';
import { useProgramDocument } from '@/contexts/ProgramContext';
import { useProcedureToEvidenceIntegration } from '@/hooks/useTaskIntegration';
import { useProgressStore } from '@/store/progress';
import AppLayout from '@/components/layout/AppLayout';
import {
  DocumentTextIcon,
  ArrowRightIcon,
  ChevronDownIcon,
  ChevronUpIcon,
  ShieldCheckIcon,
  ExclamationTriangleIcon,
  SpeakerWaveIcon,
  BookOpenIcon,
  CloudArrowUpIcon,
  EyeIcon,
  ArrowLeftIcon,
  XMarkIcon,
  DocumentIcon,
  TrashIcon,
  ArrowDownTrayIcon,
  PlusIcon,
  LinkIcon
} from '@heroicons/react/24/outline';

// Sub-documents for Prosedur dan Instruksi Kerja
const SUB_DOCUMENTS: any[] = [];

// Mock files data
const MOCK_FILES = [
  {
    id: '1',
    name: 'SOP_Procurement_Process_v1.2.pdf',
    type: 'pdf',
    size: '2.1 MB',
    uploadDate: '2024-01-15',
    uploadedBy: 'Admin Super'
  },
  {
    id: '2',
    name: 'Work_Instruction_Vendor_Evaluation.docx',
    type: 'docx',
    size: '1.5 MB',
    uploadDate: '2024-01-14',
    uploadedBy: 'Admin Biasa'
  }
];

// File Preview Modal Component
interface FilePreviewModalProps {
  isOpen: boolean;
  onClose: () => void;
  fileName: string;
  fileType: string;
  fileContent?: string;
  mimeType?: string;
}

function FilePreviewModal({ isOpen, onClose, fileName, fileType, fileContent, mimeType }: FilePreviewModalProps) {
  if (!isOpen) return null;

  const renderPreview = () => {
    const extension = fileType.toLowerCase();

    // If no content available, show placeholder
    if (!fileContent) {
      return (
        <div className="w-full h-[600px] bg-gray-100 rounded-lg flex items-center justify-center">
          <div className="text-center">
            <DocumentIcon className="h-16 w-16 text-gray-600 mx-auto mb-4" />
            <p className="font-gotham text-gray-700 mb-2">No Preview Available</p>
            <p className="font-gotham text-sm text-gray-500">{fileName}</p>
            <p className="font-gotham text-xs text-gray-400 mt-2">
              File content not available for preview.
            </p>
          </div>
        </div>
      );
    }

    if (extension === 'pdf') {
      return (
        <div className="w-full h-[600px] bg-gray-100 rounded-lg overflow-hidden">
          <iframe
            src={fileContent}
            className="w-full h-full border-0"
            title={`PDF Preview: ${fileName}`}
          />
        </div>
      );
    }

    if (['jpg', 'jpeg', 'png', 'gif', 'webp'].includes(extension)) {
      return (
        <div className="w-full h-[600px] bg-gray-100 rounded-lg overflow-hidden flex items-center justify-center">
          <img
            src={fileContent}
            alt={fileName}
            className="max-w-full max-h-full object-contain"
          />
        </div>
      );
    }

    if (['doc', 'docx'].includes(extension)) {
      return (
        <div className="w-full h-[600px] bg-gray-100 rounded-lg flex items-center justify-center">
          <div className="text-center">
            <DocumentIcon className="h-16 w-16 text-blue-600 mx-auto mb-4" />
            <p className="font-gotham text-gray-700 mb-2">Word Document</p>
            <p className="font-gotham text-sm text-gray-500">{fileName}</p>
            <p className="font-gotham text-xs text-gray-400 mt-2">
              Preview dokumen Word tidak tersedia. Silakan download untuk melihat file.
            </p>
          </div>
        </div>
      );
    }

    if (['xls', 'xlsx'].includes(extension)) {
      return (
        <div className="w-full h-[600px] bg-gray-100 rounded-lg flex items-center justify-center">
          <div className="text-center">
            <DocumentIcon className="h-16 w-16 text-green-600 mx-auto mb-4" />
            <p className="font-gotham text-gray-700 mb-2">Excel Spreadsheet</p>
            <p className="font-gotham text-sm text-gray-500">{fileName}</p>
            <p className="font-gotham text-xs text-gray-400 mt-2">
              Preview spreadsheet tidak tersedia. Silakan download untuk melihat file.
            </p>
          </div>
        </div>
      );
    }

    if (['ppt', 'pptx'].includes(extension)) {
      return (
        <div className="w-full h-[600px] bg-gray-100 rounded-lg flex items-center justify-center">
          <div className="text-center">
            <DocumentIcon className="h-16 w-16 text-orange-600 mx-auto mb-4" />
            <p className="font-gotham text-gray-700 mb-2">PowerPoint Presentation</p>
            <p className="font-gotham text-sm text-gray-500">{fileName}</p>
            <p className="font-gotham text-xs text-gray-400 mt-2">
              Preview presentasi tidak tersedia. Silakan download untuk melihat file.
            </p>
          </div>
        </div>
      );
    }

    // Default preview for unknown file types
    return (
      <div className="w-full h-[600px] bg-gray-100 rounded-lg flex items-center justify-center">
        <div className="text-center">
          <DocumentIcon className="h-16 w-16 text-gray-600 mx-auto mb-4" />
          <p className="font-gotham text-gray-700 mb-2">File Preview</p>
          <p className="font-gotham text-sm text-gray-500">{fileName}</p>
          <p className="font-gotham text-xs text-gray-400 mt-2">
            Preview tidak tersedia untuk tipe file ini. Silakan download untuk melihat file.
          </p>
        </div>
      </div>
    );
  };

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-[60]">
      <div className="bg-white rounded-xl shadow-xl max-w-6xl w-full mx-4 max-h-[95vh] overflow-hidden">
        <div className="p-6 border-b border-gray-200">
          <div className="flex items-center justify-between">
            <h2 className="font-gotham-rounded text-xl font-bold text-gray-900">
              File Preview
            </h2>
            <button
              onClick={onClose}
              className="p-2 hover:bg-gray-100 rounded-lg transition-colors"
            >
              <XMarkIcon className="h-6 w-6 text-gray-500" />
            </button>
          </div>
        </div>

        <div className="p-6">
          {renderPreview()}

          <div className="mt-6 flex justify-center space-x-3">
            <button
              onClick={() => {
                if (fileContent) {
                  const link = document.createElement('a');
                  link.href = fileContent;
                  link.download = fileName;
                  document.body.appendChild(link);
                  link.click();
                  document.body.removeChild(link);
                } else {
                  alert('File content not available for download');
                }
              }}
              className="flex items-center space-x-2 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors font-gotham font-medium"
            >
              <ArrowDownTrayIcon className="h-4 w-4" />
              <span>Download</span>
            </button>
            <button
              onClick={onClose}
              className="flex items-center space-x-2 px-4 py-2 bg-gray-600 text-white rounded-lg hover:bg-gray-700 transition-colors font-gotham font-medium"
            >
              <XMarkIcon className="h-4 w-4" />
              <span>Close</span>
            </button>
          </div>
        </div>
      </div>
    </div>
  );
}

// File Manager Modal Component
interface FileManagerModalProps {
  isOpen: boolean;
  onClose: () => void;
  sectionId: string;
  sectionTitle: string;
  userRole: string;
}

function FileManagerModal({ isOpen, onClose, sectionId, sectionTitle, userRole }: FileManagerModalProps) {
  // Store files per section in localStorage
  const [sectionFiles, setSectionFiles] = useState<Record<string, any[]>>({});

  // Load files from localStorage after component mounts
  useEffect(() => {
    const loadSectionFiles = () => {
      try {
        const saved = localStorage.getItem('doc-1-section-files');
        if (saved) {
          setSectionFiles(JSON.parse(saved));
        }
      } catch (error) {
        console.error('Error loading section files from localStorage:', error);
      }
    };

    loadSectionFiles();
  }, []);

  const [selectedFile, setSelectedFile] = useState<File | null>(null);
  const [isUploading, setIsUploading] = useState(false);
  const [showPreview, setShowPreview] = useState(false);
  const [previewFile, setPreviewFile] = useState<{name: string, type: string, content?: string, mimeType?: string}>({
    name: '',
    type: '',
    content: '',
    mimeType: ''
  });

  // Close preview when file manager is closed
  const handleClose = () => {
    setShowPreview(false);
    onClose();
  };

  // Get files for current section
  const currentSectionFiles = sectionFiles[sectionId] || [];
  const hasFile = currentSectionFiles.length > 0;

  // Check if user can upload (not karyawan or reviewer)
  const canUpload = userRole !== 'karyawan' && userRole !== 'reviewer';

  // Save to localStorage whenever sectionFiles changes
  const updateSectionFiles = (newSectionFiles: Record<string, any[]>) => {
    setSectionFiles(newSectionFiles);
    try {
      localStorage.setItem('doc-1-section-files', JSON.stringify(newSectionFiles));
    } catch (error) {
      console.error('Error saving section files to localStorage:', error);
    }
  };

  if (!isOpen) return null;

  const handleFileSelect = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (file) {
      setSelectedFile(file);
    }
  };

  const handleUpload = async () => {
    if (!selectedFile || hasFile) return; // Prevent upload if already has file

    setIsUploading(true);

    // Convert file to base64 for storage
    const fileReader = new FileReader();
    fileReader.onload = () => {
      const fileContent = fileReader.result as string;

      const newFile = {
        id: Date.now().toString(),
        name: selectedFile.name,
        type: selectedFile.name.split('.').pop() || 'unknown',
        size: `${(selectedFile.size / 1024 / 1024).toFixed(1)} MB`,
        uploadDate: new Date().toISOString().split('T')[0],
        uploadedBy: 'Current User',
        content: fileContent, // Store file content as base64
        mimeType: selectedFile.type
      };

      // Update files for this section only (max 1 file)
      const newSectionFiles = {
        ...sectionFiles,
        [sectionId]: [newFile] // Only one file per section
      };

      updateSectionFiles(newSectionFiles);
      setSelectedFile(null);
      setIsUploading(false);

      alert('File berhasil diupload!');
    };

    fileReader.onerror = () => {
      setIsUploading(false);
      alert('Error reading file!');
    };

    // Read file as data URL (base64)
    fileReader.readAsDataURL(selectedFile);
  };

  const handleDelete = (fileId: string) => {
    if (confirm('Apakah Anda yakin ingin menghapus file ini?')) {
      const newSectionFiles = {
        ...sectionFiles,
        [sectionId]: currentSectionFiles.filter(file => file.id !== fileId)
      };
      updateSectionFiles(newSectionFiles);
    }
  };

  const handleView = (file: any) => {
    setPreviewFile({
      name: file.name,
      type: file.type,
      content: file.content,
      mimeType: file.mimeType
    });
    setShowPreview(true);
  };

  const handleDownload = (fileName: string) => {
    alert(`Mengunduh file: ${fileName}`);
  };

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div className="bg-white rounded-xl shadow-xl max-w-4xl w-full max-h-[90vh] overflow-hidden">
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b border-gray-200">
          <div>
            <h2 className="font-gotham-rounded text-xl font-bold text-gray-900">
              File Manager
            </h2>
            <p className="font-gotham text-gray-600 text-sm mt-1">
              {sectionTitle}
            </p>
          </div>
          <button
            onClick={handleClose}
            className="p-2 hover:bg-gray-100 rounded-lg transition-colors"
          >
            <XMarkIcon className="h-6 w-6 text-gray-500" />
          </button>
        </div>

        <div className="p-6 max-h-[calc(90vh-120px)] overflow-y-auto">
          {/* Upload Section - Only for users who can upload */}
          {canUpload && (
            <div className="bg-gray-50 rounded-lg p-4 mb-6">
              <h3 className="font-gotham-rounded font-semibold text-gray-900 mb-3">
                Upload File Baru
              </h3>

              {hasFile ? (
                <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-3">
                  <p className="font-gotham text-yellow-800 text-sm">
                    ⚠️ Sub-dokumen ini sudah memiliki file. Hapus file yang ada terlebih dahulu untuk upload file baru.
                  </p>
                </div>
              ) : (
                <div className="space-y-3">
                  <input
                    type="file"
                    onChange={handleFileSelect}
                    className="block w-full text-sm text-gray-500 file:mr-4 file:py-2 file:px-4 file:rounded-lg file:border-0 file:text-sm file:font-medium file:bg-blue-50 file:text-blue-700 hover:file:bg-blue-100"
                    accept=".pdf,.doc,.docx,.xls,.xlsx,.ppt,.pptx"
                  />

                  {selectedFile && (
                    <div className="bg-white rounded-lg p-3 border">
                      <div className="flex items-center justify-between">
                        <div className="flex items-center space-x-3">
                          <DocumentIcon className="h-6 w-6 text-blue-600" />
                          <div>
                            <p className="font-gotham font-medium text-gray-900 text-sm">{selectedFile.name}</p>
                            <p className="font-gotham text-xs text-gray-500">
                              {(selectedFile.size / 1024 / 1024).toFixed(1)} MB
                            </p>
                          </div>
                        </div>
                        <button
                          onClick={handleUpload}
                          disabled={isUploading}
                          className="flex items-center space-x-2 px-3 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors font-gotham font-medium text-sm"
                        >
                          <CloudArrowUpIcon className="h-4 w-4" />
                          <span>{isUploading ? 'Uploading...' : 'Upload'}</span>
                        </button>
                      </div>
                    </div>
                  )}
                </div>
              )}
            </div>
          )}

          {/* Info for read-only users */}
          {!canUpload && (
            <div className="bg-blue-50 border border-blue-200 rounded-lg p-4 mb-6">
              <div className="flex items-center space-x-3">
                <EyeIcon className="h-5 w-5 text-blue-600" />
                <div>
                  <h3 className="font-gotham-rounded font-semibold text-blue-900 text-sm">
                    Mode View Only
                  </h3>
                  <p className="font-gotham text-blue-800 text-xs">
                    Anda dapat melihat dan mendownload file, namun tidak dapat mengupload file baru.
                  </p>
                </div>
              </div>
            </div>
          )}

          {/* Files List */}
          <div>
            <h3 className="font-gotham-rounded font-semibold text-gray-900 mb-3">
              File yang Tersedia
            </h3>

            <div className="space-y-2">
              {currentSectionFiles.map((file) => (
                <div key={file.id} className="bg-white border border-gray-200 rounded-lg p-4 hover:bg-gray-50 transition-colors">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center space-x-3">
                      <DocumentIcon className="h-6 w-6 text-blue-600" />
                      <div>
                        <h4 className="font-gotham font-medium text-gray-900 text-sm">{file.name}</h4>
                        <div className="flex items-center space-x-3 text-xs text-gray-500 font-gotham">
                          <span>{file.size}</span>
                          <span>•</span>
                          <span>{file.uploadDate}</span>
                          <span>•</span>
                          <span>{file.uploadedBy}</span>
                        </div>
                      </div>
                    </div>

                    <div className="flex items-center space-x-1">
                      <button
                        onClick={() => handleView(file)}
                        className="p-2 text-gray-600 hover:text-blue-600 hover:bg-blue-50 rounded-lg transition-colors"
                        title="View File"
                      >
                        <EyeIcon className="h-4 w-4" />
                      </button>
                      <button
                        onClick={() => handleDownload(file.name)}
                        className="p-2 text-gray-600 hover:text-green-600 hover:bg-green-50 rounded-lg transition-colors"
                        title="Download File"
                      >
                        <ArrowDownTrayIcon className="h-4 w-4" />
                      </button>

                      {/* Delete button - only for users who can upload */}
                      {canUpload && (
                        <button
                          onClick={() => handleDelete(file.id)}
                          className="p-2 text-gray-600 hover:text-red-600 hover:bg-red-50 rounded-lg transition-colors"
                          title="Delete File"
                        >
                          <TrashIcon className="h-4 w-4" />
                        </button>
                      )}
                    </div>
                  </div>
                </div>
              ))}

              {currentSectionFiles.length === 0 && (
                <div className="text-center py-8">
                  <DocumentIcon className="h-12 w-12 text-gray-400 mx-auto mb-3" />
                  <p className="font-gotham text-gray-500 text-sm">
                    Belum ada file yang diupload untuk sub-dokumen ini
                  </p>
                  <p className="font-gotham text-gray-400 text-xs mt-1">
                    Maksimal 1 file per sub-dokumen
                  </p>
                </div>
              )}
            </div>
          </div>
        </div>
      </div>

      {/* File Preview Modal */}
      <FilePreviewModal
        isOpen={showPreview}
        onClose={() => setShowPreview(false)}
        fileName={previewFile.name}
        fileType={previewFile.type}
        fileContent={previewFile.content}
        mimeType={previewFile.mimeType}
      />
    </div>
  );
}

// Add Section Modal Component
interface AddSectionModalProps {
  isOpen: boolean;
  onClose: () => void;
  onSave: (title: string, description: string) => void;
  subDocTitle: string;
}

function AddSectionModal({ isOpen, onClose, onSave, subDocTitle }: AddSectionModalProps) {
  const [title, setTitle] = useState('');
  const [description, setDescription] = useState('');

  if (!isOpen) return null;

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    if (title.trim()) {
      onSave(title.trim(), description.trim());
      setTitle('');
      setDescription('');
    }
  };

  const handleClose = () => {
    setTitle('');
    setDescription('');
    onClose();
  };

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white rounded-xl p-6 w-full max-w-md mx-4">
        <div className="flex items-center justify-between mb-4">
          <h3 className="font-gotham-rounded text-lg font-semibold text-gray-900">
            Tambah Section Baru
          </h3>
          <button
            onClick={handleClose}
            className="p-2 hover:bg-gray-100 rounded-lg transition-colors"
          >
            <XMarkIcon className="h-5 w-5 text-gray-500" />
          </button>
        </div>

        <p className="font-gotham text-sm text-gray-600 mb-4">
          Menambah section baru untuk: <span className="font-medium">{subDocTitle}</span>
        </p>

        <form onSubmit={handleSubmit} className="space-y-4">
          <div>
            <label className="block font-gotham text-sm font-medium text-gray-700 mb-2">
              Judul Section
            </label>
            <input
              type="text"
              value={title}
              onChange={(e) => setTitle(e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 font-gotham"
              placeholder="Masukkan judul section..."
              required
            />
          </div>

          <div>
            <label className="block font-gotham text-sm font-medium text-gray-700 mb-2">
              Deskripsi <span className="text-gray-400 font-normal">(opsional)</span>
            </label>
            <textarea
              value={description}
              onChange={(e) => setDescription(e.target.value)}
              rows={3}
              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 font-gotham"
              placeholder="Masukkan deskripsi section (opsional)..."
            />
          </div>

          <div className="flex space-x-3 pt-2">
            <button
              type="button"
              onClick={handleClose}
              className="flex-1 px-4 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors font-gotham font-medium"
            >
              Batal
            </button>
            <button
              type="submit"
              disabled={!title.trim()}
              className="flex-1 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:bg-gray-300 disabled:cursor-not-allowed transition-colors font-gotham font-medium"
            >
              Tambah Section
            </button>
          </div>
        </form>
      </div>
    </div>
  );
}

// Add Sub-Section Modal Component
interface AddSubSectionModalProps {
  isOpen: boolean;
  onClose: () => void;
  onSave: (title: string, description: string, type: 'upload' | 'form', formUrl?: string, mappingData?: {
    targetSectionId: string;
    targetSectionTitle: string;
  }) => void;
  sectionTitle: string;
  sectionId: string;
}

function AddSubSectionModal({ isOpen, onClose, onSave, sectionTitle, sectionId }: AddSubSectionModalProps) {
  const [title, setTitle] = useState('');
  const [description, setDescription] = useState('');
  const [type, setType] = useState<'upload' | 'form'>('upload');
  const [formUrl, setFormUrl] = useState('');
  const [enableMapping, setEnableMapping] = useState(false);
  const [selectedTargetSection, setSelectedTargetSection] = useState('');

  const { getDocumentProgress } = useProgressStore();

  // Get available sections from Evidence Mapping Klausul (doc-3)
  const doc3Progress = getDocumentProgress('doc-3');
  const availableTargetSections = doc3Progress?.sections || [];

  if (!isOpen) return null;

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    if (title.trim() && (type === 'upload' || (type === 'form' && formUrl.trim()))) {
      onSave(title.trim(), description.trim(), type, type === 'form' ? formUrl.trim() : undefined);
      setTitle('');
      setDescription('');
      setType('upload');
      setFormUrl('');
    }
  };

  const handleClose = () => {
    setTitle('');
    setDescription('');
    setType('upload');
    setFormUrl('');
    onClose();
  };

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white rounded-xl p-6 w-full max-w-md mx-4">
        <div className="flex items-center justify-between mb-4">
          <h3 className="font-gotham-rounded text-lg font-semibold text-gray-900">
            Tambah Sub-Section Baru
          </h3>
          <button
            onClick={handleClose}
            className="p-2 hover:bg-gray-100 rounded-lg transition-colors"
          >
            <XMarkIcon className="h-5 w-5 text-gray-500" />
          </button>
        </div>

        <p className="font-gotham text-sm text-gray-600 mb-4">
          Menambah sub-section baru untuk: <span className="font-medium">{sectionTitle}</span>
        </p>

        <form onSubmit={handleSubmit} className="space-y-4">
          <div>
            <label className="block font-gotham text-sm font-medium text-gray-700 mb-2">
              Judul Sub-Section
            </label>
            <input
              type="text"
              value={title}
              onChange={(e) => setTitle(e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 font-gotham"
              placeholder="Masukkan judul sub-section..."
              required
            />
          </div>

          <div>
            <label className="block font-gotham text-sm font-medium text-gray-700 mb-2">
              Deskripsi <span className="text-gray-400 font-normal">(opsional)</span>
            </label>
            <textarea
              value={description}
              onChange={(e) => setDescription(e.target.value)}
              rows={3}
              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 font-gotham"
              placeholder="Masukkan deskripsi sub-section (opsional)..."
            />
          </div>

          <div>
            <label className="block font-gotham text-sm font-medium text-gray-700 mb-2">
              Tipe Sub-Section
            </label>
            <div className="space-y-3">
              <label className="flex items-center p-3 border border-gray-200 rounded-lg hover:bg-gray-50 cursor-pointer">
                <input
                  type="radio"
                  name="subsectionType"
                  value="upload"
                  checked={type === 'upload'}
                  onChange={(e) => setType(e.target.value as 'upload' | 'form')}
                  className="mr-3"
                />
                <div className="flex items-center space-x-3">
                  <div className="p-2 bg-blue-100 text-blue-600 rounded-lg">
                    <DocumentTextIcon className="h-5 w-5" />
                  </div>
                  <div>
                    <div className="font-gotham font-medium text-gray-900">Upload Dokumen</div>
                    <div className="font-gotham text-sm text-gray-600">Sub-section untuk upload file dokumen</div>
                  </div>
                </div>
              </label>

              <label className="flex items-center p-3 border border-gray-200 rounded-lg hover:bg-gray-50 cursor-pointer">
                <input
                  type="radio"
                  name="subsectionType"
                  value="form"
                  checked={type === 'form'}
                  onChange={(e) => setType(e.target.value as 'upload' | 'form')}
                  className="mr-3"
                />
                <div className="flex items-center space-x-3">
                  <div className="p-2 bg-green-100 text-green-600 rounded-lg">
                    <DocumentIcon className="h-5 w-5" />
                  </div>
                  <div>
                    <div className="font-gotham font-medium text-gray-900">Isi Formulir</div>
                    <div className="font-gotham text-sm text-gray-600">Sub-section untuk mengisi formulir online</div>
                  </div>
                </div>
              </label>
            </div>
          </div>

          {type === 'form' && (
            <div>
              <label className="block font-gotham text-sm font-medium text-gray-700 mb-2">
                URL Formulir
              </label>
              <input
                type="url"
                value={formUrl}
                onChange={(e) => setFormUrl(e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 font-gotham"
                placeholder="Contoh: /forms/risk-assessment"
                required
              />
              <p className="font-gotham text-xs text-gray-500 mt-1">
                Masukkan URL relatif atau absolut untuk formulir
              </p>
            </div>
          )}

          <div className="flex space-x-3 pt-2">
            <button
              type="button"
              onClick={handleClose}
              className="flex-1 px-4 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors font-gotham font-medium"
            >
              Batal
            </button>
            <button
              type="submit"
              disabled={!title.trim() || (type === 'form' && !formUrl.trim())}
              className="flex-1 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:bg-gray-300 disabled:cursor-not-allowed transition-colors font-gotham font-medium"
            >
              Tambah Sub-Section
            </button>
          </div>
        </form>
      </div>
    </div>
  );
}

// Delete Confirmation Modal Component
interface DeleteConfirmModalProps {
  isOpen: boolean;
  onClose: () => void;
  onConfirm: () => void;
  title: string;
  type: 'section' | 'subsection';
}

function DeleteConfirmModal({ isOpen, onClose, onConfirm, title, type }: DeleteConfirmModalProps) {
  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white rounded-xl p-6 w-full max-w-md mx-4">
        <div className="flex items-center justify-between mb-4">
          <h3 className="font-gotham-rounded text-lg font-semibold text-red-900">
            Hapus {type === 'section' ? 'Section' : 'Sub-Section'}
          </h3>
          <button
            onClick={onClose}
            className="p-2 hover:bg-gray-100 rounded-lg transition-colors"
          >
            <XMarkIcon className="h-5 w-5 text-gray-500" />
          </button>
        </div>

        <div className="mb-6">
          <div className="flex items-center space-x-3 mb-4">
            <div className="p-3 bg-red-100 rounded-lg">
              <TrashIcon className="h-6 w-6 text-red-600" />
            </div>
            <div>
              <p className="font-gotham font-medium text-gray-900">
                Apakah Anda yakin ingin menghapus {type === 'section' ? 'section' : 'sub-section'} ini?
              </p>
              <p className="font-gotham text-sm text-gray-600 mt-1">
                <span className="font-medium">"{title}"</span>
              </p>
            </div>
          </div>

          <div className="bg-red-50 border border-red-200 rounded-lg p-3">
            <p className="font-gotham text-sm text-red-800">
              <span className="font-medium">Peringatan:</span> Tindakan ini tidak dapat dibatalkan.
              {type === 'section'
                ? ' Semua sub-section dan file yang terkait akan ikut terhapus.'
                : ' File yang terkait dengan sub-section ini akan ikut terhapus.'
              }
            </p>
          </div>
        </div>

        <div className="flex space-x-3">
          <button
            onClick={onClose}
            className="flex-1 px-4 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors font-gotham font-medium"
          >
            Batal
          </button>
          <button
            onClick={onConfirm}
            className="flex-1 px-4 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700 transition-colors font-gotham font-medium"
          >
            Hapus
          </button>
        </div>
      </div>
    </div>
  );
}

export default function PedomanPeraturanPage() {
  const { user } = useAuth();
  const {
    dynamicSections: dynamicMainSections,
    dynamicSubSections,
    updateDynamicSections: updateDynamicMainSections,
    updateDynamicSubSections
  } = useProgramDocument('doc2');

  const [expandedSection, setExpandedSection] = useState<string | null>(null);
  const [showFileManager, setShowFileManager] = useState(false);
  const [currentSection, setCurrentSection] = useState<string>('');
  const [showAddSection, setShowAddSection] = useState(false);
  const [showAddSubSection, setShowAddSubSection] = useState(false);
  const [showDeleteConfirm, setShowDeleteConfirm] = useState(false);
  const [currentSubDocId, setCurrentSubDocId] = useState<string>('');
  const [deleteTarget, setDeleteTarget] = useState<{type: 'section' | 'subsection', id: string, title: string}>({
    type: 'section',
    id: '',
    title: ''
  });



  if (!user) return null;



  const handleSectionToggle = (sectionId: string) => {
    setExpandedSection(expandedSection === sectionId ? null : sectionId);
  };

  const handleSubSectionToggle = (subSectionId: string) => {
    setExpandedSection(expandedSection === subSectionId ? null : subSectionId);
  };

  const handleUpload = (subSectionId: string) => {
    setCurrentSection(subSectionId);
    setShowFileManager(true);
  };

  const handleViewFile = (subSectionId: string) => {
    // Get files from localStorage for this section
    const savedFiles = localStorage.getItem('doc-1-section-files');
    const sectionFiles = savedFiles ? JSON.parse(savedFiles) : {};
    const filesForSection = sectionFiles[subSectionId] || [];

    if (filesForSection.length === 0) {
      alert('Belum ada file yang diupload untuk sub-dokumen ini.');
      return;
    }

    // If there's a file, show it in the file manager
    setCurrentSection(subSectionId);
    setShowFileManager(true);
  };

  // Handle adding new main section (E, F, G, etc.)
  const handleAddMainSection = () => {
    setShowAddSection(true);
  };

  // Handle adding new sub-section (A.4, B.1, etc.)
  const handleAddSubSection = (subDocId: string) => {
    setCurrentSubDocId(subDocId);
    setShowAddSubSection(true);
  };

  // Save new main section
  const handleSaveNewMainSection = (title: string, description: string) => {
    const newSectionId = `dynamic-section-${Date.now()}`;

    const newSection = {
      id: newSectionId,
      title: title,
      description: description,
      iconType: 'DocumentTextIcon', // Store icon type as string
      color: 'bg-purple-100 text-purple-600',
      hoverColor: 'hover:border-purple-500 hover:bg-purple-50',
      iconHoverColor: 'hover:text-purple-600',
      subSections: [],
      createdAt: new Date().toISOString(),
      createdBy: user.name
    };

    const updatedSections = [...dynamicMainSections, newSection];
    updateDynamicMainSections(updatedSections);
    setShowAddSection(false);
  };

  // Save new sub-section
  const handleSaveNewSubSection = (title: string, description: string, type: 'upload' | 'form', formUrl?: string) => {
    const subDocSubSections = dynamicSubSections[currentSubDocId] || [];
    const newSubSectionId = `${currentSubDocId}-subsection-${Date.now()}`;

    const newSubSection = {
      id: newSubSectionId,
      title: title,
      description: description,
      type: type,
      formUrl: type === 'form' ? formUrl : undefined,
      createdAt: new Date().toISOString(),
      createdBy: user.name
    };

    const updatedSubSections = {
      ...dynamicSubSections,
      [currentSubDocId]: [...subDocSubSections, newSubSection]
    };

    updateDynamicSubSections(updatedSubSections);
    setShowAddSubSection(false);
    setCurrentSubDocId('');
  };

  // Handle delete section/sub-section
  const handleDeleteRequest = (type: 'section' | 'subsection', id: string, title: string) => {
    setDeleteTarget({ type, id, title });
    setShowDeleteConfirm(true);
  };

  const handleConfirmDelete = () => {
    if (deleteTarget.type === 'section') {
      // Delete main section
      const updatedSections = dynamicMainSections.filter(section => section.id !== deleteTarget.id);
      updateDynamicMainSections(updatedSections);

      // Also delete all sub-sections for this section
      const updatedSubSections = { ...dynamicSubSections };
      delete updatedSubSections[deleteTarget.id];
      updateDynamicSubSections(updatedSubSections);

      // Delete all files for this section and its sub-sections
      cleanupSectionFiles(deleteTarget.id);

    } else if (deleteTarget.type === 'subsection') {
      // Delete sub-section
      const sectionId = findSectionIdForSubSection(deleteTarget.id);
      if (sectionId) {
        const updatedSubSections = {
          ...dynamicSubSections,
          [sectionId]: dynamicSubSections[sectionId].filter(subSection => subSection.id !== deleteTarget.id)
        };
        updateDynamicSubSections(updatedSubSections);

        // Delete files for this sub-section
        cleanupSubSectionFiles(deleteTarget.id);
      }
    }

    setShowDeleteConfirm(false);
    setDeleteTarget({ type: 'section', id: '', title: '' });
  };

  // Helper function to find section ID for a sub-section
  const findSectionIdForSubSection = (subSectionId: string): string | null => {
    for (const sectionId in dynamicSubSections) {
      if (dynamicSubSections[sectionId].some(subSection => subSection.id === subSectionId)) {
        return sectionId;
      }
    }
    return null;
  };

  // Cleanup files when deleting section/sub-section
  const cleanupSectionFiles = (sectionId: string) => {
    try {
      const savedFiles = localStorage.getItem('doc-1-section-files');
      if (savedFiles) {
        const sectionFiles = JSON.parse(savedFiles);

        // Remove files for the section itself
        delete sectionFiles[sectionId];

        // Remove files for all sub-sections of this section
        const subSections = dynamicSubSections[sectionId] || [];
        subSections.forEach(subSection => {
          delete sectionFiles[subSection.id];
        });

        localStorage.setItem('doc-1-section-files', JSON.stringify(sectionFiles));
      }
    } catch (error) {
      console.error('Error cleaning up section files:', error);
    }
  };

  const cleanupSubSectionFiles = (subSectionId: string) => {
    try {
      const savedFiles = localStorage.getItem('doc-1-section-files');
      if (savedFiles) {
        const sectionFiles = JSON.parse(savedFiles);
        delete sectionFiles[subSectionId];
        localStorage.setItem('doc-1-section-files', JSON.stringify(sectionFiles));
      }
    } catch (error) {
      console.error('Error cleaning up sub-section files:', error);
    }
  };

  // Get icon component from string identifier
  const getIconComponent = (iconType: string) => {
    const iconMap: Record<string, any> = {
      'DocumentTextIcon': DocumentTextIcon,
      'ShieldCheckIcon': ShieldCheckIcon,
      'BookOpenIcon': BookOpenIcon,
      'ExclamationTriangleIcon': ExclamationTriangleIcon,
      'SpeakerWaveIcon': SpeakerWaveIcon
    };
    return iconMap[iconType] || DocumentTextIcon;
  };

  // Get all sections (static + dynamic)
  const getAllSections = () => {
    // Add icon component and ensure all required properties for dynamic sections
    const dynamicSectionsWithIcons = dynamicMainSections.map(section => ({
      ...section,
      icon: getIconComponent(section.iconType || 'DocumentTextIcon'),
      subSections: section.subSections || [], // Ensure subSections exists
      color: section.color || 'bg-purple-100 text-purple-600',
      hoverColor: section.hoverColor || 'hover:border-purple-500 hover:bg-purple-50',
      iconHoverColor: section.iconHoverColor || 'hover:text-purple-600'
    }));
    return [...SUB_DOCUMENTS, ...dynamicSectionsWithIcons];
  };

  // Get all sub-sections for a section (static + dynamic)
  const getAllSubSections = (sectionId: string) => {
    const staticSection = SUB_DOCUMENTS.find(doc => doc.id === sectionId);
    const staticSubSections = staticSection ? staticSection.subSections : [];
    const dynamicSubSectionsForSection = dynamicSubSections[sectionId] || [];
    return [...staticSubSections, ...dynamicSubSectionsForSection];
  };

  // Check if section can be deleted (only dynamic sections)
  const canDeleteSection = (sectionId: string) => {
    return dynamicMainSections.some(section => section.id === sectionId);
  };

  // Check if sub-section can be deleted (only dynamic sub-sections)
  const canDeleteSubSection = (subSectionId: string) => {
    for (const sectionId in dynamicSubSections) {
      if (dynamicSubSections[sectionId].some(subSection => subSection.id === subSectionId)) {
        return true;
      }
    }
    return false;
  };

  const handleBack = () => {
    window.history.back();
  };

  const getSectionTitle = (sectionId: string) => {
    const sectionTitles: Record<string, string> = {
      'manual-smap': 'Manual SMAP',
      'struktur-organisasi': 'Struktur Organisasi SMAP Telkom',
      'kebijakan-anti-suap': 'Kebijakan Anti Suap Telkom',
      'formulir-risk-assessment': 'Formulir Penilaian Risiko Penyuapan (Risk Assessment)',
      'sub-doc-2': 'Code of Conduct',
      'sub-doc-3': 'Conflict of Interest',
      'sub-doc-4': 'Whistle Blower System'
    };

    // Check if it's a dynamic main section
    const dynamicMainSection = dynamicMainSections.find(section => section.id === sectionId);
    if (dynamicMainSection) {
      return dynamicMainSection.title;
    }

    // Check if it's a dynamic sub-section
    for (const subDocId in dynamicSubSections) {
      const subSections = dynamicSubSections[subDocId];
      const dynamicSubSection = subSections.find(subSection => subSection.id === sectionId);
      if (dynamicSubSection) {
        return dynamicSubSection.title;
      }
    }

    return sectionTitles[sectionId] || sectionId;
  };

  const hasFileInSection = (sectionId: string) => {
    const savedFiles = localStorage.getItem('doc-1-section-files');
    const sectionFiles = savedFiles ? JSON.parse(savedFiles) : {};
    const filesForSection = sectionFiles[sectionId] || [];
    return filesForSection.length > 0;
  };

  return (
    <AppLayout>
      <div className="container mx-auto px-4 py-8">
        <div className="max-w-6xl mx-auto">
          {/* Header */}
          <div className="mb-8">
            <div className="flex items-center space-x-3 mb-4">
              <div className="p-3 bg-red-100 rounded-xl">
                <DocumentTextIcon className="h-8 w-8 text-red-600" />
              </div>
              <div>
                <h1 className="font-gotham-rounded text-3xl font-bold text-gray-900">
                  Prosedur dan Instruksi Kerja
                </h1>
                <p className="font-gotham text-gray-600 mt-1">
                  Prosedur operasional standar dan instruksi kerja
                </p>
              </div>
            </div>
            
            {/* Breadcrumb */}
            <nav className="flex items-center space-x-2 text-sm text-gray-500 font-gotham mb-4">
              <a href="/dashboard" className="hover:text-red-600 transition-colors">
                Dashboard
              </a>
              <span>›</span>
              <span className="text-gray-900">Prosedur dan Instruksi Kerja</span>
            </nav>

            {/* Back Button */}
            <button
              onClick={handleBack}
              className="flex items-center space-x-2 px-4 py-2 bg-gray-100 text-gray-700 rounded-lg hover:bg-gray-200 transition-colors font-gotham font-medium"
            >
              <ArrowLeftIcon className="h-4 w-4" />
              <span>Kembali</span>
            </button>
          </div>

          {/* Sub-Documents List */}
          <div className="space-y-6">
            {getAllSections().map((subDoc, index) => {
              const IconComponent = subDoc.icon;
              const isExpanded = expandedSection === subDoc.id;

              return (
                <div
                  key={subDoc.id}
                  className="border border-gray-200 rounded-xl"
                >
                  {/* Main Section Header */}
                  <div
                    className={`p-6 cursor-pointer hover:bg-gray-50 transition-all duration-200 ${subDoc.hoverColor}`}
                    onClick={() => handleSectionToggle(subDoc.id)}
                  >
                    <div className="flex items-start justify-between">
                      <div className="flex items-start space-x-4">
                        <div className={`p-3 rounded-xl ${subDoc.color}`}>
                          <IconComponent className="h-6 w-6" />
                        </div>
                        <div className="flex-1">
                          <h3 className="font-gotham-rounded text-lg font-semibold text-gray-900 mb-2">
                            {String.fromCharCode(65 + index)}. {subDoc.title}
                          </h3>
                          <p className="font-gotham text-gray-600 text-sm leading-relaxed">
                            {subDoc.description}
                          </p>
                        </div>
                      </div>
                      <div className="flex items-center space-x-2">
                        {/* Delete Button - only for dynamic sections and admin roles */}
                        {canDeleteSection(subDoc.id) && (user.role === 'admin_super' || user.role === 'admin_biasa') && (
                          <button
                            onClick={(e) => {
                              e.stopPropagation(); // Prevent section toggle
                              handleDeleteRequest('section', subDoc.id, subDoc.title);
                            }}
                            className="p-2 border border-red-300 rounded-lg hover:bg-red-50 hover:border-red-400 transition-all duration-200 group"
                            title="Hapus Section"
                          >
                            <TrashIcon className="h-4 w-4 text-red-500 group-hover:text-red-600" />
                          </button>
                        )}

                        {/* Expand/Collapse Arrow */}
                        <div className={`p-2 border border-gray-300 rounded-lg transition-all duration-200 ${subDoc.hoverColor}`}>
                          {isExpanded ? (
                            <ChevronUpIcon className={`h-5 w-5 text-gray-600 transition-colors duration-200 ${subDoc.iconHoverColor}`} />
                          ) : (
                            <ChevronDownIcon className={`h-5 w-5 text-gray-600 transition-colors duration-200 ${subDoc.iconHoverColor}`} />
                          )}
                        </div>
                      </div>
                    </div>
                  </div>

                  {/* Expanded Content */}
                  {isExpanded && (getAllSubSections(subDoc.id).length > 0 || (user.role === 'admin_super' || user.role === 'admin_biasa')) && (
                    <div className="border-t border-gray-200 bg-gray-50">
                      <div className="p-6">
                        <div className="space-y-4">
                          {getAllSubSections(subDoc.id).map((subSection, subIndex) => (
                            <div
                              key={subSection.id}
                              className="bg-white border border-gray-200 rounded-lg p-4"
                            >
                              <div className="flex items-center justify-between">
                                <div className="flex-1">
                                  <div className="flex items-center space-x-2 mb-2">
                                    <div className={`p-1 rounded ${
                                      subSection.type === 'form'
                                        ? 'bg-green-100 text-green-600'
                                        : 'bg-blue-100 text-blue-600'
                                    }`}>
                                      {subSection.type === 'form' ? (
                                        <DocumentIcon className="h-3 w-3" />
                                      ) : (
                                        <DocumentTextIcon className="h-3 w-3" />
                                      )}
                                    </div>
                                    <h4 className="font-gotham-rounded text-base font-semibold text-gray-900">
                                      {String.fromCharCode(65 + index)}.{subIndex + 1} {subSection.title}
                                    </h4>
                                    <span className={`px-2 py-0.5 text-xs font-medium rounded-full ${
                                      subSection.type === 'form'
                                        ? 'bg-green-100 text-green-800'
                                        : 'bg-blue-100 text-blue-800'
                                    }`}>
                                      {subSection.type === 'form' ? 'Form' : 'Upload'}
                                    </span>
                                  </div>
                                  <p className="font-gotham text-gray-600 text-sm">
                                    {subSection.description}
                                  </p>
                                  {subSection.type === 'form' && subSection.formUrl && (
                                    <p className="font-gotham text-xs text-blue-600 mt-1">
                                      URL: {subSection.formUrl}
                                    </p>
                                  )}
                                </div>
                                <div className="flex items-center space-x-2 ml-4">
                                  {subSection.type === 'form' ? (
                                    /* Form button - for form type subsections */
                                    <button
                                      onClick={() => window.open(subSection.formUrl, '_blank')}
                                      className="flex items-center space-x-2 px-3 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors text-sm font-gotham font-medium"
                                    >
                                      <DocumentIcon className="h-4 w-4" />
                                      <span>Isi Form</span>
                                    </button>
                                  ) : (
                                    /* Upload and View buttons - for upload type subsections */
                                    <>
                                      {/* Upload button - only for admin, scoopers */}
                                      {(user.role === 'admin_super' || user.role === 'admin_biasa' || user.role === 'scoopers') && (
                                        <button
                                          onClick={() => handleUpload(subSection.id)}
                                          className="flex items-center space-x-2 px-3 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors text-sm font-gotham font-medium"
                                        >
                                          <CloudArrowUpIcon className="h-4 w-4" />
                                          <span>Upload</span>
                                        </button>
                                      )}

                                      {/* View File button - for all roles */}
                                      <button
                                        onClick={() => handleViewFile(subSection.id)}
                                        className={`flex items-center space-x-2 px-3 py-2 rounded-lg transition-colors text-sm font-gotham font-medium ${
                                          hasFileInSection(subSection.id)
                                            ? 'bg-green-600 text-white hover:bg-green-700'
                                            : 'bg-gray-400 text-white hover:bg-gray-500'
                                        }`}
                                      >
                                        <EyeIcon className="h-4 w-4" />
                                        <span>{hasFileInSection(subSection.id) ? 'View File' : 'No File'}</span>
                                      </button>
                                    </>
                                  )}

                                  {/* Delete button - only for dynamic sub-sections and admin roles */}
                                  {canDeleteSubSection(subSection.id) && (user.role === 'admin_super' || user.role === 'admin_biasa') && (
                                    <button
                                      onClick={() => handleDeleteRequest('subsection', subSection.id, subSection.title)}
                                      className="flex items-center space-x-2 px-3 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700 transition-colors text-sm font-gotham font-medium"
                                      title="Hapus Sub-Section"
                                    >
                                      <TrashIcon className="h-4 w-4" />
                                      <span>Delete</span>
                                    </button>
                                  )}

                                  {/* Arrow button for Risk Assessment Form - for all roles */}
                                  {subSection.id === 'formulir-risk-assessment' && (
                                    <>
                                      <button
                                        onClick={() => window.open('/forms/risk-assessment', '_blank')}
                                        className="flex items-center space-x-2 px-3 py-2 bg-orange-600 text-white rounded-lg hover:bg-orange-700 transition-colors text-sm font-gotham font-medium"
                                        title="Buka Formulir Risk Assessment"
                                      >
                                        <ArrowRightIcon className="h-4 w-4" />
                                        <span>Isi Formulir</span>
                                      </button>

                                      {/* View button for Admin Super and Admin Biasa only */}
                                      {(user.role === 'admin_super' || user.role === 'admin_biasa') && (
                                        <button
                                          onClick={() => window.open('/forms/risk-assessment/view', '_blank')}
                                          className="flex items-center space-x-2 px-3 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors text-sm font-gotham font-medium"
                                          title="Lihat Formulir yang Telah Diisi"
                                        >
                                          <EyeIcon className="h-4 w-4" />
                                          <span>Lihat Formulir</span>
                                        </button>
                                      )}
                                    </>
                                  )}
                                </div>
                              </div>
                            </div>
                          ))}

                          {/* Add Sub-Section Button - only for Super Admin and Admin */}
                          {(user.role === 'admin_super' || user.role === 'admin_biasa') && (
                            <div className="border-2 border-dashed border-gray-300 rounded-lg p-4 text-center hover:border-blue-400 hover:bg-blue-50 transition-all duration-200">
                              <button
                                onClick={() => handleAddSubSection(subDoc.id)}
                                className="flex items-center justify-center space-x-2 mx-auto px-3 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors font-gotham font-medium text-sm"
                              >
                                <PlusIcon className="h-4 w-4" />
                                <span>Tambah Sub-Section</span>
                              </button>
                              <p className="font-gotham text-xs text-gray-500 mt-1">
                                Tambahkan sub-section baru untuk {subDoc.title}
                              </p>
                            </div>
                          )}
                        </div>
                      </div>
                    </div>
                  )}

                  {/* No Sub-sections - Show Upload Section */}
                  {isExpanded && getAllSubSections(subDoc.id).length === 0 && (
                    <div className="border-t border-gray-200 bg-gray-50 p-6">
                      <div className="bg-white border border-gray-200 rounded-lg p-4">
                        <div className="flex items-center justify-between">
                          <div className="flex-1">
                            <h4 className="font-gotham-rounded text-base font-semibold text-gray-900 mb-1">
                              {String.fromCharCode(65 + index)}.1 Upload Dokumen {subDoc.title}
                            </h4>
                            <p className="font-gotham text-gray-600 text-sm">
                              Upload file dokumen untuk {subDoc.title}
                            </p>
                          </div>
                          <div className="flex items-center space-x-2 ml-4">
                            {/* Upload button - only for admin, scoopers */}
                            {(user.role === 'admin_super' || user.role === 'admin_biasa' || user.role === 'scoopers') && (
                              <button
                                onClick={() => handleUpload(subDoc.id)}
                                className="flex items-center space-x-2 px-3 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors text-sm font-gotham font-medium"
                              >
                                <CloudArrowUpIcon className="h-4 w-4" />
                                <span>Upload</span>
                              </button>
                            )}

                            {/* View File button - for all roles */}
                            <button
                              onClick={() => handleViewFile(subDoc.id)}
                              className={`flex items-center space-x-2 px-3 py-2 rounded-lg transition-colors text-sm font-gotham font-medium ${
                                hasFileInSection(subDoc.id)
                                  ? 'bg-green-600 text-white hover:bg-green-700'
                                  : 'bg-gray-400 text-white hover:bg-gray-500'
                              }`}
                            >
                              <EyeIcon className="h-4 w-4" />
                              <span>{hasFileInSection(subDoc.id) ? 'View File' : 'No File'}</span>
                            </button>
                          </div>
                        </div>
                      </div>
                    </div>
                  )}
                </div>
              );
            })}

            {/* Add Section Button - only for Super Admin and Admin */}
            {(user.role === 'admin_super' || user.role === 'admin_biasa') && (
              <div className="border-2 border-dashed border-gray-300 rounded-xl p-6 text-center hover:border-blue-400 hover:bg-blue-50 transition-all duration-200">
                <button
                  onClick={handleAddMainSection}
                  className="flex items-center justify-center space-x-2 mx-auto px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors font-gotham font-medium"
                >
                  <PlusIcon className="h-5 w-5" />
                  <span>Tambah Section Baru</span>
                </button>
                <p className="font-gotham text-sm text-gray-500 mt-2">
                  Tambahkan section baru untuk Prosedur dan Instruksi Kerja
                </p>
              </div>
            )}
          </div>

          {/* Info Section */}
          <div className="mt-12 bg-blue-50 rounded-xl p-6">
            <div className="flex items-start space-x-3">
              <div className="p-2 bg-blue-100 rounded-lg">
                <DocumentTextIcon className="h-5 w-5 text-blue-600" />
              </div>
              <div>
                <h3 className="font-gotham-rounded text-lg font-semibold text-blue-900 mb-2">
                  Informasi Dokumen
                </h3>
                <p className="font-gotham text-blue-800 text-sm leading-relaxed">
                  Dokumen Prosedur dan Instruksi Kerja terdiri dari 4 sub-dokumen utama yang mengatur
                  prosedur operasional standar dan instruksi kerja. Setiap sub-dokumen memiliki fokus khusus dalam
                  mendukung implementasi proses bisnis yang efektif dan efisien.
                </p>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* File Manager Modal */}
      <FileManagerModal
        isOpen={showFileManager}
        onClose={() => setShowFileManager(false)}
        sectionId={currentSection}
        sectionTitle={getSectionTitle(currentSection)}
        userRole={user.role}
      />

      {/* Add Section Modal */}
      <AddSectionModal
        isOpen={showAddSection}
        onClose={() => setShowAddSection(false)}
        onSave={handleSaveNewMainSection}
        subDocTitle="Prosedur dan Instruksi Kerja"
      />

      {/* Add Sub-Section Modal */}
      <AddSubSectionModal
        isOpen={showAddSubSection}
        onClose={() => setShowAddSubSection(false)}
        onSave={handleSaveNewSubSection}
        sectionTitle={getSectionTitle(currentSubDocId)}
      />

      {/* Delete Confirmation Modal */}
      <DeleteConfirmModal
        isOpen={showDeleteConfirm}
        onClose={() => setShowDeleteConfirm(false)}
        onConfirm={handleConfirmDelete}
        title={deleteTarget.title}
        type={deleteTarget.type}
      />
    </AppLayout>
  );
}
