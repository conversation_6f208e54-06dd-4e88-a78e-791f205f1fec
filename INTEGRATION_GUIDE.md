# Panduan Integrasi: Prosedur dan Instruksi Kerja ↔ Evidence Mapping Klausul

## Overview

Sistem integrasi ini menghubungkan section "Prosedur dan Instruksi Kerja" (doc-2) dengan "Evidence Mapping Klausul" (doc-3) sehingga progress dapat tersinkronisasi secara otomatis.

## Fitur Utama

### 1. Task Mapping
- Super Admin dan Admin dapat membuat task/subsection di "Prosedur dan Instruksi Kerja"
- Saat membuat subsection, dapat memilih untuk menghubungkannya dengan klausul di "Evidence Mapping Klausul"
- Sistem akan membuat mapping otomatis antara kedua section

### 2. Real-time Progress Sync
- Ketika Scoopers melakukan aksi (upload file/isi formulir) di "Prosedur dan Instruksi Kerja"
- Progress akan otomatis terhitung di "Evidence Mapping Klausul"
- Sinkronisasi terjadi secara real-time menggunakan event system

### 3. Visual Indicators
- Badge integrasi menunjukkan subsection yang terhubung
- Status indicator menampilkan status sinkronisasi
- Progress bar terintegrasi di kedua section

## Cara Penggunaan

### Untuk Super Admin dan Admin

#### 1. Membuat Task dengan Mapping
1. Buka section "Prosedur dan Instruksi Kerja"
2. Klik "Tambah Sub-Section Baru" pada section yang diinginkan
3. Isi form subsection:
   - Judul Sub-Section
   - Deskripsi
   - Tipe (Upload/Form)
   - URL Formulir (jika tipe Form)
4. **Centang "Hubungkan dengan Evidence Mapping Klausul"**
5. Pilih klausul target dari dropdown
6. Klik "Tambah Sub-Section"

#### 2. Monitoring Progress
- Lihat badge integrasi di subsection yang terhubung
- Monitor status sinkronisasi melalui indicator
- Cek progress terintegrasi di dashboard

### Untuk Scoopers

#### 1. Melakukan Aksi di Prosedur dan Instruksi Kerja
- Upload file pada subsection yang diminta
- Isi formulir sesuai instruksi
- Progress akan otomatis tersinkronisasi

#### 2. Melihat Progress di Evidence Mapping Klausul
- Buka section "Evidence Mapping Klausul"
- Lihat progress yang telah tersinkronisasi
- Progress akan update secara real-time

## Komponen Teknis

### 1. Task Mapping Store (`src/store/taskMapping.ts`)
- Mengelola hubungan antara task di doc-2 dan klausul di doc-3
- Menyimpan mapping data dan status sinkronisasi
- Menyediakan fungsi untuk CRUD mapping

### 2. Integration Hook (`src/hooks/useTaskIntegration.ts`)
- Hook utama untuk mengelola integrasi
- Menyediakan fungsi untuk membuat mapping
- Menangani sinkronisasi progress

### 3. Progress Tracking (`src/hooks/useSubsectionActions.ts`)
- Dimodifikasi untuk mendukung sinkronisasi
- Mengirim event saat ada perubahan progress
- Mendengarkan event dari mapping system

### 4. UI Components (`src/components/ui/IntegrationStatus.tsx`)
- `IntegrationBadge`: Badge untuk menunjukkan subsection terhubung
- `IntegrationStatus`: Indicator status sinkronisasi
- `IntegrationSummary`: Ringkasan integrasi
- `SyncProgressIndicator`: Progress bar sinkronisasi

## Alur Kerja Integrasi

### 1. Pembuatan Mapping
```
Admin membuat subsection → Pilih mapping → Sistem buat TaskMapping → Simpan ke store
```

### 2. Sinkronisasi Progress
```
Scoopers upload file → SubsectionAction dibuat → Event dipicu → TaskMapping sync → Target progress update
```

### 3. Real-time Update
```
Source progress change → Event broadcast → Target document listen → UI update → Visual feedback
```

## Data Structure

### TaskMapping
```typescript
interface TaskMapping {
  id: string;
  sourceDocumentId: string; // "doc-2"
  sourceSectionId: string;
  sourceSubsectionId: string;
  targetDocumentId: string; // "doc-3"
  targetSectionId: string;
  targetSubsectionId?: string;
  mappingType: 'one_to_one' | 'one_to_many' | 'many_to_one';
  createdAt: string;
  createdBy: string;
  isActive: boolean;
}
```

### TaskMappingProgress
```typescript
interface TaskMappingProgress {
  mappingId: string;
  sourceProgress: number;
  targetProgress: number;
  lastSyncAt: string;
  syncStatus: 'synced' | 'pending' | 'error';
}
```

## Event System

### Events yang Dipicu
- `subsectionActionsChange`: Ketika ada perubahan action di subsection
- `taskMappingSync`: Ketika terjadi sinkronisasi mapping
- `syncStart`: Ketika proses sync dimulai
- `syncComplete`: Ketika sync berhasil
- `syncError`: Ketika terjadi error sync

### Event Listeners
- `useSubsectionActions`: Mendengarkan perubahan action
- `useTaskIntegration`: Mendengarkan event mapping
- `IntegrationStatus`: Mendengarkan status sync

## Troubleshooting

### 1. Progress Tidak Tersinkronisasi
- Periksa mapping di TaskMapping store
- Cek console untuk error event
- Pastikan kedua dokumen aktif

### 2. Badge Integrasi Tidak Muncul
- Pastikan subsection memiliki `mappedToClausul` property
- Cek import komponen IntegrationBadge
- Verifikasi data mapping

### 3. Status Sync Pending
- Tunggu beberapa detik untuk auto-sync
- Refresh halaman jika diperlukan
- Cek network connectivity

## Best Practices

### 1. Untuk Admin
- Selalu buat mapping saat membuat subsection baru
- Monitor status integrasi secara berkala
- Dokumentasikan mapping yang dibuat

### 2. Untuk Developer
- Gunakan TypeScript interfaces yang sudah disediakan
- Ikuti pattern event-driven untuk sinkronisasi
- Test integrasi secara menyeluruh

### 3. Untuk Maintenance
- Backup data mapping secara berkala
- Monitor performance event system
- Update dokumentasi saat ada perubahan

## Future Enhancements

1. **Bulk Mapping**: Kemampuan mapping multiple subsections sekaligus
2. **Advanced Analytics**: Dashboard analytics untuk monitoring integrasi
3. **Conflict Resolution**: Sistem untuk menangani konflik sinkronisasi
4. **Audit Trail**: Log lengkap untuk tracking perubahan mapping
5. **API Integration**: REST API untuk integrasi dengan sistem eksternal

## Support

Untuk pertanyaan atau issue terkait integrasi, silakan hubungi tim development atau buat issue di repository project.
