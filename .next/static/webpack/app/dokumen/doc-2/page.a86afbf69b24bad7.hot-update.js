"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/dokumen/doc-2/page",{

/***/ "(app-pages-browser)/./src/app/dokumen/doc-2/page.tsx":
/*!****************************************!*\
  !*** ./src/app/dokumen/doc-2/page.tsx ***!
  \****************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ PedomanPeraturanPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _store_auth__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/store/auth */ \"(app-pages-browser)/./src/store/auth.ts\");\n/* harmony import */ var _contexts_ProgramContext__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/contexts/ProgramContext */ \"(app-pages-browser)/./src/contexts/ProgramContext.tsx\");\n/* harmony import */ var _components_layout_AppLayout__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/layout/AppLayout */ \"(app-pages-browser)/./src/components/layout/AppLayout.tsx\");\n/* harmony import */ var _barrel_optimize_names_ArrowDownTrayIcon_ArrowLeftIcon_ArrowRightIcon_BookOpenIcon_ChevronDownIcon_ChevronUpIcon_CloudArrowUpIcon_DocumentIcon_DocumentTextIcon_ExclamationTriangleIcon_EyeIcon_PlusIcon_ShieldCheckIcon_SpeakerWaveIcon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowDownTrayIcon,ArrowLeftIcon,ArrowRightIcon,BookOpenIcon,ChevronDownIcon,ChevronUpIcon,CloudArrowUpIcon,DocumentIcon,DocumentTextIcon,ExclamationTriangleIcon,EyeIcon,PlusIcon,ShieldCheckIcon,SpeakerWaveIcon,TrashIcon,XMarkIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/DocumentIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowDownTrayIcon_ArrowLeftIcon_ArrowRightIcon_BookOpenIcon_ChevronDownIcon_ChevronUpIcon_CloudArrowUpIcon_DocumentIcon_DocumentTextIcon_ExclamationTriangleIcon_EyeIcon_PlusIcon_ShieldCheckIcon_SpeakerWaveIcon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowDownTrayIcon,ArrowLeftIcon,ArrowRightIcon,BookOpenIcon,ChevronDownIcon,ChevronUpIcon,CloudArrowUpIcon,DocumentIcon,DocumentTextIcon,ExclamationTriangleIcon,EyeIcon,PlusIcon,ShieldCheckIcon,SpeakerWaveIcon,TrashIcon,XMarkIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/XMarkIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowDownTrayIcon_ArrowLeftIcon_ArrowRightIcon_BookOpenIcon_ChevronDownIcon_ChevronUpIcon_CloudArrowUpIcon_DocumentIcon_DocumentTextIcon_ExclamationTriangleIcon_EyeIcon_PlusIcon_ShieldCheckIcon_SpeakerWaveIcon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowDownTrayIcon,ArrowLeftIcon,ArrowRightIcon,BookOpenIcon,ChevronDownIcon,ChevronUpIcon,CloudArrowUpIcon,DocumentIcon,DocumentTextIcon,ExclamationTriangleIcon,EyeIcon,PlusIcon,ShieldCheckIcon,SpeakerWaveIcon,TrashIcon,XMarkIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/ArrowDownTrayIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowDownTrayIcon_ArrowLeftIcon_ArrowRightIcon_BookOpenIcon_ChevronDownIcon_ChevronUpIcon_CloudArrowUpIcon_DocumentIcon_DocumentTextIcon_ExclamationTriangleIcon_EyeIcon_PlusIcon_ShieldCheckIcon_SpeakerWaveIcon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowDownTrayIcon,ArrowLeftIcon,ArrowRightIcon,BookOpenIcon,ChevronDownIcon,ChevronUpIcon,CloudArrowUpIcon,DocumentIcon,DocumentTextIcon,ExclamationTriangleIcon,EyeIcon,PlusIcon,ShieldCheckIcon,SpeakerWaveIcon,TrashIcon,XMarkIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/CloudArrowUpIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowDownTrayIcon_ArrowLeftIcon_ArrowRightIcon_BookOpenIcon_ChevronDownIcon_ChevronUpIcon_CloudArrowUpIcon_DocumentIcon_DocumentTextIcon_ExclamationTriangleIcon_EyeIcon_PlusIcon_ShieldCheckIcon_SpeakerWaveIcon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowDownTrayIcon,ArrowLeftIcon,ArrowRightIcon,BookOpenIcon,ChevronDownIcon,ChevronUpIcon,CloudArrowUpIcon,DocumentIcon,DocumentTextIcon,ExclamationTriangleIcon,EyeIcon,PlusIcon,ShieldCheckIcon,SpeakerWaveIcon,TrashIcon,XMarkIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/EyeIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowDownTrayIcon_ArrowLeftIcon_ArrowRightIcon_BookOpenIcon_ChevronDownIcon_ChevronUpIcon_CloudArrowUpIcon_DocumentIcon_DocumentTextIcon_ExclamationTriangleIcon_EyeIcon_PlusIcon_ShieldCheckIcon_SpeakerWaveIcon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowDownTrayIcon,ArrowLeftIcon,ArrowRightIcon,BookOpenIcon,ChevronDownIcon,ChevronUpIcon,CloudArrowUpIcon,DocumentIcon,DocumentTextIcon,ExclamationTriangleIcon,EyeIcon,PlusIcon,ShieldCheckIcon,SpeakerWaveIcon,TrashIcon,XMarkIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/TrashIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowDownTrayIcon_ArrowLeftIcon_ArrowRightIcon_BookOpenIcon_ChevronDownIcon_ChevronUpIcon_CloudArrowUpIcon_DocumentIcon_DocumentTextIcon_ExclamationTriangleIcon_EyeIcon_PlusIcon_ShieldCheckIcon_SpeakerWaveIcon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowDownTrayIcon,ArrowLeftIcon,ArrowRightIcon,BookOpenIcon,ChevronDownIcon,ChevronUpIcon,CloudArrowUpIcon,DocumentIcon,DocumentTextIcon,ExclamationTriangleIcon,EyeIcon,PlusIcon,ShieldCheckIcon,SpeakerWaveIcon,TrashIcon,XMarkIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/DocumentTextIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowDownTrayIcon_ArrowLeftIcon_ArrowRightIcon_BookOpenIcon_ChevronDownIcon_ChevronUpIcon_CloudArrowUpIcon_DocumentIcon_DocumentTextIcon_ExclamationTriangleIcon_EyeIcon_PlusIcon_ShieldCheckIcon_SpeakerWaveIcon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowDownTrayIcon,ArrowLeftIcon,ArrowRightIcon,BookOpenIcon,ChevronDownIcon,ChevronUpIcon,CloudArrowUpIcon,DocumentIcon,DocumentTextIcon,ExclamationTriangleIcon,EyeIcon,PlusIcon,ShieldCheckIcon,SpeakerWaveIcon,TrashIcon,XMarkIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/ShieldCheckIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowDownTrayIcon_ArrowLeftIcon_ArrowRightIcon_BookOpenIcon_ChevronDownIcon_ChevronUpIcon_CloudArrowUpIcon_DocumentIcon_DocumentTextIcon_ExclamationTriangleIcon_EyeIcon_PlusIcon_ShieldCheckIcon_SpeakerWaveIcon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowDownTrayIcon,ArrowLeftIcon,ArrowRightIcon,BookOpenIcon,ChevronDownIcon,ChevronUpIcon,CloudArrowUpIcon,DocumentIcon,DocumentTextIcon,ExclamationTriangleIcon,EyeIcon,PlusIcon,ShieldCheckIcon,SpeakerWaveIcon,TrashIcon,XMarkIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/BookOpenIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowDownTrayIcon_ArrowLeftIcon_ArrowRightIcon_BookOpenIcon_ChevronDownIcon_ChevronUpIcon_CloudArrowUpIcon_DocumentIcon_DocumentTextIcon_ExclamationTriangleIcon_EyeIcon_PlusIcon_ShieldCheckIcon_SpeakerWaveIcon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowDownTrayIcon,ArrowLeftIcon,ArrowRightIcon,BookOpenIcon,ChevronDownIcon,ChevronUpIcon,CloudArrowUpIcon,DocumentIcon,DocumentTextIcon,ExclamationTriangleIcon,EyeIcon,PlusIcon,ShieldCheckIcon,SpeakerWaveIcon,TrashIcon,XMarkIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/ExclamationTriangleIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowDownTrayIcon_ArrowLeftIcon_ArrowRightIcon_BookOpenIcon_ChevronDownIcon_ChevronUpIcon_CloudArrowUpIcon_DocumentIcon_DocumentTextIcon_ExclamationTriangleIcon_EyeIcon_PlusIcon_ShieldCheckIcon_SpeakerWaveIcon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowDownTrayIcon,ArrowLeftIcon,ArrowRightIcon,BookOpenIcon,ChevronDownIcon,ChevronUpIcon,CloudArrowUpIcon,DocumentIcon,DocumentTextIcon,ExclamationTriangleIcon,EyeIcon,PlusIcon,ShieldCheckIcon,SpeakerWaveIcon,TrashIcon,XMarkIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/SpeakerWaveIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowDownTrayIcon_ArrowLeftIcon_ArrowRightIcon_BookOpenIcon_ChevronDownIcon_ChevronUpIcon_CloudArrowUpIcon_DocumentIcon_DocumentTextIcon_ExclamationTriangleIcon_EyeIcon_PlusIcon_ShieldCheckIcon_SpeakerWaveIcon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowDownTrayIcon,ArrowLeftIcon,ArrowRightIcon,BookOpenIcon,ChevronDownIcon,ChevronUpIcon,CloudArrowUpIcon,DocumentIcon,DocumentTextIcon,ExclamationTriangleIcon,EyeIcon,PlusIcon,ShieldCheckIcon,SpeakerWaveIcon,TrashIcon,XMarkIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/ArrowLeftIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowDownTrayIcon_ArrowLeftIcon_ArrowRightIcon_BookOpenIcon_ChevronDownIcon_ChevronUpIcon_CloudArrowUpIcon_DocumentIcon_DocumentTextIcon_ExclamationTriangleIcon_EyeIcon_PlusIcon_ShieldCheckIcon_SpeakerWaveIcon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowDownTrayIcon,ArrowLeftIcon,ArrowRightIcon,BookOpenIcon,ChevronDownIcon,ChevronUpIcon,CloudArrowUpIcon,DocumentIcon,DocumentTextIcon,ExclamationTriangleIcon,EyeIcon,PlusIcon,ShieldCheckIcon,SpeakerWaveIcon,TrashIcon,XMarkIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/ChevronUpIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowDownTrayIcon_ArrowLeftIcon_ArrowRightIcon_BookOpenIcon_ChevronDownIcon_ChevronUpIcon_CloudArrowUpIcon_DocumentIcon_DocumentTextIcon_ExclamationTriangleIcon_EyeIcon_PlusIcon_ShieldCheckIcon_SpeakerWaveIcon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowDownTrayIcon,ArrowLeftIcon,ArrowRightIcon,BookOpenIcon,ChevronDownIcon,ChevronUpIcon,CloudArrowUpIcon,DocumentIcon,DocumentTextIcon,ExclamationTriangleIcon,EyeIcon,PlusIcon,ShieldCheckIcon,SpeakerWaveIcon,TrashIcon,XMarkIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/ChevronDownIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowDownTrayIcon_ArrowLeftIcon_ArrowRightIcon_BookOpenIcon_ChevronDownIcon_ChevronUpIcon_CloudArrowUpIcon_DocumentIcon_DocumentTextIcon_ExclamationTriangleIcon_EyeIcon_PlusIcon_ShieldCheckIcon_SpeakerWaveIcon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowDownTrayIcon,ArrowLeftIcon,ArrowRightIcon,BookOpenIcon,ChevronDownIcon,ChevronUpIcon,CloudArrowUpIcon,DocumentIcon,DocumentTextIcon,ExclamationTriangleIcon,EyeIcon,PlusIcon,ShieldCheckIcon,SpeakerWaveIcon,TrashIcon,XMarkIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/ArrowRightIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowDownTrayIcon_ArrowLeftIcon_ArrowRightIcon_BookOpenIcon_ChevronDownIcon_ChevronUpIcon_CloudArrowUpIcon_DocumentIcon_DocumentTextIcon_ExclamationTriangleIcon_EyeIcon_PlusIcon_ShieldCheckIcon_SpeakerWaveIcon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowDownTrayIcon,ArrowLeftIcon,ArrowRightIcon,BookOpenIcon,ChevronDownIcon,ChevronUpIcon,CloudArrowUpIcon,DocumentIcon,DocumentTextIcon,ExclamationTriangleIcon,EyeIcon,PlusIcon,ShieldCheckIcon,SpeakerWaveIcon,TrashIcon,XMarkIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/PlusIcon.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$(), _s1 = $RefreshSig$(), _s2 = $RefreshSig$(), _s3 = $RefreshSig$();\n\n\n\n\n\n// Sub-documents for Prosedur dan Instruksi Kerja\nconst SUB_DOCUMENTS = [];\n// Mock files data\nconst MOCK_FILES = [\n    {\n        id: '1',\n        name: 'SOP_Procurement_Process_v1.2.pdf',\n        type: 'pdf',\n        size: '2.1 MB',\n        uploadDate: '2024-01-15',\n        uploadedBy: 'Admin Super'\n    },\n    {\n        id: '2',\n        name: 'Work_Instruction_Vendor_Evaluation.docx',\n        type: 'docx',\n        size: '1.5 MB',\n        uploadDate: '2024-01-14',\n        uploadedBy: 'Admin Biasa'\n    }\n];\nfunction FilePreviewModal(param) {\n    let { isOpen, onClose, fileName, fileType, fileContent, mimeType } = param;\n    if (!isOpen) return null;\n    const renderPreview = ()=>{\n        const extension = fileType.toLowerCase();\n        // If no content available, show placeholder\n        if (!fileContent) {\n            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"w-full h-[600px] bg-gray-100 rounded-lg flex items-center justify-center\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-center\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowDownTrayIcon_ArrowLeftIcon_ArrowRightIcon_BookOpenIcon_ChevronDownIcon_ChevronUpIcon_CloudArrowUpIcon_DocumentIcon_DocumentTextIcon_ExclamationTriangleIcon_EyeIcon_PlusIcon_ShieldCheckIcon_SpeakerWaveIcon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                            className: \"h-16 w-16 text-gray-600 mx-auto mb-4\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-2/page.tsx\",\n                            lineNumber: 73,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"font-gotham text-gray-700 mb-2\",\n                            children: \"No Preview Available\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-2/page.tsx\",\n                            lineNumber: 74,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"font-gotham text-sm text-gray-500\",\n                            children: fileName\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-2/page.tsx\",\n                            lineNumber: 75,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"font-gotham text-xs text-gray-400 mt-2\",\n                            children: \"File content not available for preview.\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-2/page.tsx\",\n                            lineNumber: 76,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-2/page.tsx\",\n                    lineNumber: 72,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-2/page.tsx\",\n                lineNumber: 71,\n                columnNumber: 9\n            }, this);\n        }\n        if (extension === 'pdf') {\n            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"w-full h-[600px] bg-gray-100 rounded-lg overflow-hidden\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"iframe\", {\n                    src: fileContent,\n                    className: \"w-full h-full border-0\",\n                    title: \"PDF Preview: \".concat(fileName)\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-2/page.tsx\",\n                    lineNumber: 87,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-2/page.tsx\",\n                lineNumber: 86,\n                columnNumber: 9\n            }, this);\n        }\n        if ([\n            'jpg',\n            'jpeg',\n            'png',\n            'gif',\n            'webp'\n        ].includes(extension)) {\n            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"w-full h-[600px] bg-gray-100 rounded-lg overflow-hidden flex items-center justify-center\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                    src: fileContent,\n                    alt: fileName,\n                    className: \"max-w-full max-h-full object-contain\"\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-2/page.tsx\",\n                    lineNumber: 99,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-2/page.tsx\",\n                lineNumber: 98,\n                columnNumber: 9\n            }, this);\n        }\n        if ([\n            'doc',\n            'docx'\n        ].includes(extension)) {\n            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"w-full h-[600px] bg-gray-100 rounded-lg flex items-center justify-center\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-center\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowDownTrayIcon_ArrowLeftIcon_ArrowRightIcon_BookOpenIcon_ChevronDownIcon_ChevronUpIcon_CloudArrowUpIcon_DocumentIcon_DocumentTextIcon_ExclamationTriangleIcon_EyeIcon_PlusIcon_ShieldCheckIcon_SpeakerWaveIcon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                            className: \"h-16 w-16 text-blue-600 mx-auto mb-4\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-2/page.tsx\",\n                            lineNumber: 112,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"font-gotham text-gray-700 mb-2\",\n                            children: \"Word Document\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-2/page.tsx\",\n                            lineNumber: 113,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"font-gotham text-sm text-gray-500\",\n                            children: fileName\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-2/page.tsx\",\n                            lineNumber: 114,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"font-gotham text-xs text-gray-400 mt-2\",\n                            children: \"Preview dokumen Word tidak tersedia. Silakan download untuk melihat file.\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-2/page.tsx\",\n                            lineNumber: 115,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-2/page.tsx\",\n                    lineNumber: 111,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-2/page.tsx\",\n                lineNumber: 110,\n                columnNumber: 9\n            }, this);\n        }\n        if ([\n            'xls',\n            'xlsx'\n        ].includes(extension)) {\n            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"w-full h-[600px] bg-gray-100 rounded-lg flex items-center justify-center\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-center\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowDownTrayIcon_ArrowLeftIcon_ArrowRightIcon_BookOpenIcon_ChevronDownIcon_ChevronUpIcon_CloudArrowUpIcon_DocumentIcon_DocumentTextIcon_ExclamationTriangleIcon_EyeIcon_PlusIcon_ShieldCheckIcon_SpeakerWaveIcon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                            className: \"h-16 w-16 text-green-600 mx-auto mb-4\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-2/page.tsx\",\n                            lineNumber: 127,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"font-gotham text-gray-700 mb-2\",\n                            children: \"Excel Spreadsheet\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-2/page.tsx\",\n                            lineNumber: 128,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"font-gotham text-sm text-gray-500\",\n                            children: fileName\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-2/page.tsx\",\n                            lineNumber: 129,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"font-gotham text-xs text-gray-400 mt-2\",\n                            children: \"Preview spreadsheet tidak tersedia. Silakan download untuk melihat file.\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-2/page.tsx\",\n                            lineNumber: 130,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-2/page.tsx\",\n                    lineNumber: 126,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-2/page.tsx\",\n                lineNumber: 125,\n                columnNumber: 9\n            }, this);\n        }\n        if ([\n            'ppt',\n            'pptx'\n        ].includes(extension)) {\n            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"w-full h-[600px] bg-gray-100 rounded-lg flex items-center justify-center\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-center\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowDownTrayIcon_ArrowLeftIcon_ArrowRightIcon_BookOpenIcon_ChevronDownIcon_ChevronUpIcon_CloudArrowUpIcon_DocumentIcon_DocumentTextIcon_ExclamationTriangleIcon_EyeIcon_PlusIcon_ShieldCheckIcon_SpeakerWaveIcon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                            className: \"h-16 w-16 text-orange-600 mx-auto mb-4\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-2/page.tsx\",\n                            lineNumber: 142,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"font-gotham text-gray-700 mb-2\",\n                            children: \"PowerPoint Presentation\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-2/page.tsx\",\n                            lineNumber: 143,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"font-gotham text-sm text-gray-500\",\n                            children: fileName\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-2/page.tsx\",\n                            lineNumber: 144,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"font-gotham text-xs text-gray-400 mt-2\",\n                            children: \"Preview presentasi tidak tersedia. Silakan download untuk melihat file.\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-2/page.tsx\",\n                            lineNumber: 145,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-2/page.tsx\",\n                    lineNumber: 141,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-2/page.tsx\",\n                lineNumber: 140,\n                columnNumber: 9\n            }, this);\n        }\n        // Default preview for unknown file types\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"w-full h-[600px] bg-gray-100 rounded-lg flex items-center justify-center\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowDownTrayIcon_ArrowLeftIcon_ArrowRightIcon_BookOpenIcon_ChevronDownIcon_ChevronUpIcon_CloudArrowUpIcon_DocumentIcon_DocumentTextIcon_ExclamationTriangleIcon_EyeIcon_PlusIcon_ShieldCheckIcon_SpeakerWaveIcon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                        className: \"h-16 w-16 text-gray-600 mx-auto mb-4\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-2/page.tsx\",\n                        lineNumber: 157,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"font-gotham text-gray-700 mb-2\",\n                        children: \"File Preview\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-2/page.tsx\",\n                        lineNumber: 158,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"font-gotham text-sm text-gray-500\",\n                        children: fileName\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-2/page.tsx\",\n                        lineNumber: 159,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"font-gotham text-xs text-gray-400 mt-2\",\n                        children: \"Preview tidak tersedia untuk tipe file ini. Silakan download untuk melihat file.\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-2/page.tsx\",\n                        lineNumber: 160,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-2/page.tsx\",\n                lineNumber: 156,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-2/page.tsx\",\n            lineNumber: 155,\n            columnNumber: 7\n        }, this);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-[60]\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"bg-white rounded-xl shadow-xl max-w-6xl w-full mx-4 max-h-[95vh] overflow-hidden\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"p-6 border-b border-gray-200\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center justify-between\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                className: \"font-gotham-rounded text-xl font-bold text-gray-900\",\n                                children: \"File Preview\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-2/page.tsx\",\n                                lineNumber: 173,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: onClose,\n                                className: \"p-2 hover:bg-gray-100 rounded-lg transition-colors\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowDownTrayIcon_ArrowLeftIcon_ArrowRightIcon_BookOpenIcon_ChevronDownIcon_ChevronUpIcon_CloudArrowUpIcon_DocumentIcon_DocumentTextIcon_ExclamationTriangleIcon_EyeIcon_PlusIcon_ShieldCheckIcon_SpeakerWaveIcon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                    className: \"h-6 w-6 text-gray-500\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-2/page.tsx\",\n                                    lineNumber: 180,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-2/page.tsx\",\n                                lineNumber: 176,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-2/page.tsx\",\n                        lineNumber: 172,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-2/page.tsx\",\n                    lineNumber: 171,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"p-6\",\n                    children: [\n                        renderPreview(),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"mt-6 flex justify-center space-x-3\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: ()=>{\n                                        if (fileContent) {\n                                            const link = document.createElement('a');\n                                            link.href = fileContent;\n                                            link.download = fileName;\n                                            document.body.appendChild(link);\n                                            link.click();\n                                            document.body.removeChild(link);\n                                        } else {\n                                            alert('File content not available for download');\n                                        }\n                                    },\n                                    className: \"flex items-center space-x-2 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors font-gotham font-medium\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowDownTrayIcon_ArrowLeftIcon_ArrowRightIcon_BookOpenIcon_ChevronDownIcon_ChevronUpIcon_CloudArrowUpIcon_DocumentIcon_DocumentTextIcon_ExclamationTriangleIcon_EyeIcon_PlusIcon_ShieldCheckIcon_SpeakerWaveIcon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                            className: \"h-4 w-4\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-2/page.tsx\",\n                                            lineNumber: 204,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            children: \"Download\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-2/page.tsx\",\n                                            lineNumber: 205,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-2/page.tsx\",\n                                    lineNumber: 189,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: onClose,\n                                    className: \"flex items-center space-x-2 px-4 py-2 bg-gray-600 text-white rounded-lg hover:bg-gray-700 transition-colors font-gotham font-medium\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowDownTrayIcon_ArrowLeftIcon_ArrowRightIcon_BookOpenIcon_ChevronDownIcon_ChevronUpIcon_CloudArrowUpIcon_DocumentIcon_DocumentTextIcon_ExclamationTriangleIcon_EyeIcon_PlusIcon_ShieldCheckIcon_SpeakerWaveIcon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                            className: \"h-4 w-4\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-2/page.tsx\",\n                                            lineNumber: 211,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            children: \"Close\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-2/page.tsx\",\n                                            lineNumber: 212,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-2/page.tsx\",\n                                    lineNumber: 207,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-2/page.tsx\",\n                            lineNumber: 188,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-2/page.tsx\",\n                    lineNumber: 185,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-2/page.tsx\",\n            lineNumber: 170,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-2/page.tsx\",\n        lineNumber: 169,\n        columnNumber: 5\n    }, this);\n}\n_c = FilePreviewModal;\nfunction FileManagerModal(param) {\n    let { isOpen, onClose, sectionId, sectionTitle, userRole } = param;\n    _s();\n    // Store files per section in localStorage\n    const [sectionFiles, setSectionFiles] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({});\n    // Load files from localStorage after component mounts\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"FileManagerModal.useEffect\": ()=>{\n            const loadSectionFiles = {\n                \"FileManagerModal.useEffect.loadSectionFiles\": ()=>{\n                    try {\n                        const saved = localStorage.getItem('doc-1-section-files');\n                        if (saved) {\n                            setSectionFiles(JSON.parse(saved));\n                        }\n                    } catch (error) {\n                        console.error('Error loading section files from localStorage:', error);\n                    }\n                }\n            }[\"FileManagerModal.useEffect.loadSectionFiles\"];\n            loadSectionFiles();\n        }\n    }[\"FileManagerModal.useEffect\"], []);\n    const [selectedFile, setSelectedFile] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [isUploading, setIsUploading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [showPreview, setShowPreview] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [previewFile, setPreviewFile] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        name: '',\n        type: '',\n        content: '',\n        mimeType: ''\n    });\n    // Close preview when file manager is closed\n    const handleClose = ()=>{\n        setShowPreview(false);\n        onClose();\n    };\n    // Get files for current section\n    const currentSectionFiles = sectionFiles[sectionId] || [];\n    const hasFile = currentSectionFiles.length > 0;\n    // Check if user can upload (not karyawan or reviewer)\n    const canUpload = userRole !== 'karyawan' && userRole !== 'reviewer';\n    // Save to localStorage whenever sectionFiles changes\n    const updateSectionFiles = (newSectionFiles)=>{\n        setSectionFiles(newSectionFiles);\n        try {\n            localStorage.setItem('doc-1-section-files', JSON.stringify(newSectionFiles));\n        } catch (error) {\n            console.error('Error saving section files to localStorage:', error);\n        }\n    };\n    if (!isOpen) return null;\n    const handleFileSelect = (event)=>{\n        var _event_target_files;\n        const file = (_event_target_files = event.target.files) === null || _event_target_files === void 0 ? void 0 : _event_target_files[0];\n        if (file) {\n            setSelectedFile(file);\n        }\n    };\n    const handleUpload = async ()=>{\n        if (!selectedFile || hasFile) return; // Prevent upload if already has file\n        setIsUploading(true);\n        // Convert file to base64 for storage\n        const fileReader = new FileReader();\n        fileReader.onload = ()=>{\n            const fileContent = fileReader.result;\n            const newFile = {\n                id: Date.now().toString(),\n                name: selectedFile.name,\n                type: selectedFile.name.split('.').pop() || 'unknown',\n                size: \"\".concat((selectedFile.size / 1024 / 1024).toFixed(1), \" MB\"),\n                uploadDate: new Date().toISOString().split('T')[0],\n                uploadedBy: 'Current User',\n                content: fileContent,\n                mimeType: selectedFile.type\n            };\n            // Update files for this section only (max 1 file)\n            const newSectionFiles = {\n                ...sectionFiles,\n                [sectionId]: [\n                    newFile\n                ] // Only one file per section\n            };\n            updateSectionFiles(newSectionFiles);\n            setSelectedFile(null);\n            setIsUploading(false);\n            alert('File berhasil diupload!');\n        };\n        fileReader.onerror = ()=>{\n            setIsUploading(false);\n            alert('Error reading file!');\n        };\n        // Read file as data URL (base64)\n        fileReader.readAsDataURL(selectedFile);\n    };\n    const handleDelete = (fileId)=>{\n        if (confirm('Apakah Anda yakin ingin menghapus file ini?')) {\n            const newSectionFiles = {\n                ...sectionFiles,\n                [sectionId]: currentSectionFiles.filter((file)=>file.id !== fileId)\n            };\n            updateSectionFiles(newSectionFiles);\n        }\n    };\n    const handleView = (file)=>{\n        setPreviewFile({\n            name: file.name,\n            type: file.type,\n            content: file.content,\n            mimeType: file.mimeType\n        });\n        setShowPreview(true);\n    };\n    const handleDownload = (fileName)=>{\n        alert(\"Mengunduh file: \".concat(fileName));\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-white rounded-xl shadow-xl max-w-4xl w-full max-h-[90vh] overflow-hidden\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center justify-between p-6 border-b border-gray-200\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                        className: \"font-gotham-rounded text-xl font-bold text-gray-900\",\n                                        children: \"File Manager\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-2/page.tsx\",\n                                        lineNumber: 365,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"font-gotham text-gray-600 text-sm mt-1\",\n                                        children: sectionTitle\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-2/page.tsx\",\n                                        lineNumber: 368,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-2/page.tsx\",\n                                lineNumber: 364,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: handleClose,\n                                className: \"p-2 hover:bg-gray-100 rounded-lg transition-colors\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowDownTrayIcon_ArrowLeftIcon_ArrowRightIcon_BookOpenIcon_ChevronDownIcon_ChevronUpIcon_CloudArrowUpIcon_DocumentIcon_DocumentTextIcon_ExclamationTriangleIcon_EyeIcon_PlusIcon_ShieldCheckIcon_SpeakerWaveIcon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                    className: \"h-6 w-6 text-gray-500\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-2/page.tsx\",\n                                    lineNumber: 376,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-2/page.tsx\",\n                                lineNumber: 372,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-2/page.tsx\",\n                        lineNumber: 363,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"p-6 max-h-[calc(90vh-120px)] overflow-y-auto\",\n                        children: [\n                            canUpload && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-gray-50 rounded-lg p-4 mb-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"font-gotham-rounded font-semibold text-gray-900 mb-3\",\n                                        children: \"Upload File Baru\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-2/page.tsx\",\n                                        lineNumber: 384,\n                                        columnNumber: 15\n                                    }, this),\n                                    hasFile ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-yellow-50 border border-yellow-200 rounded-lg p-3\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"font-gotham text-yellow-800 text-sm\",\n                                            children: \"⚠️ Sub-dokumen ini sudah memiliki file. Hapus file yang ada terlebih dahulu untuk upload file baru.\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-2/page.tsx\",\n                                            lineNumber: 390,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-2/page.tsx\",\n                                        lineNumber: 389,\n                                        columnNumber: 17\n                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-3\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                type: \"file\",\n                                                onChange: handleFileSelect,\n                                                className: \"block w-full text-sm text-gray-500 file:mr-4 file:py-2 file:px-4 file:rounded-lg file:border-0 file:text-sm file:font-medium file:bg-blue-50 file:text-blue-700 hover:file:bg-blue-100\",\n                                                accept: \".pdf,.doc,.docx,.xls,.xlsx,.ppt,.pptx\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-2/page.tsx\",\n                                                lineNumber: 396,\n                                                columnNumber: 19\n                                            }, this),\n                                            selectedFile && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"bg-white rounded-lg p-3 border\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center justify-between\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center space-x-3\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowDownTrayIcon_ArrowLeftIcon_ArrowRightIcon_BookOpenIcon_ChevronDownIcon_ChevronUpIcon_CloudArrowUpIcon_DocumentIcon_DocumentTextIcon_ExclamationTriangleIcon_EyeIcon_PlusIcon_ShieldCheckIcon_SpeakerWaveIcon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                                    className: \"h-6 w-6 text-blue-600\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-2/page.tsx\",\n                                                                    lineNumber: 407,\n                                                                    columnNumber: 27\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                            className: \"font-gotham font-medium text-gray-900 text-sm\",\n                                                                            children: selectedFile.name\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-2/page.tsx\",\n                                                                            lineNumber: 409,\n                                                                            columnNumber: 29\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                            className: \"font-gotham text-xs text-gray-500\",\n                                                                            children: [\n                                                                                (selectedFile.size / 1024 / 1024).toFixed(1),\n                                                                                \" MB\"\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-2/page.tsx\",\n                                                                            lineNumber: 410,\n                                                                            columnNumber: 29\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-2/page.tsx\",\n                                                                    lineNumber: 408,\n                                                                    columnNumber: 27\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-2/page.tsx\",\n                                                            lineNumber: 406,\n                                                            columnNumber: 25\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                            onClick: handleUpload,\n                                                            disabled: isUploading,\n                                                            className: \"flex items-center space-x-2 px-3 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors font-gotham font-medium text-sm\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowDownTrayIcon_ArrowLeftIcon_ArrowRightIcon_BookOpenIcon_ChevronDownIcon_ChevronUpIcon_CloudArrowUpIcon_DocumentIcon_DocumentTextIcon_ExclamationTriangleIcon_EyeIcon_PlusIcon_ShieldCheckIcon_SpeakerWaveIcon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                                    className: \"h-4 w-4\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-2/page.tsx\",\n                                                                    lineNumber: 420,\n                                                                    columnNumber: 27\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    children: isUploading ? 'Uploading...' : 'Upload'\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-2/page.tsx\",\n                                                                    lineNumber: 421,\n                                                                    columnNumber: 27\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-2/page.tsx\",\n                                                            lineNumber: 415,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-2/page.tsx\",\n                                                    lineNumber: 405,\n                                                    columnNumber: 23\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-2/page.tsx\",\n                                                lineNumber: 404,\n                                                columnNumber: 21\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-2/page.tsx\",\n                                        lineNumber: 395,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-2/page.tsx\",\n                                lineNumber: 383,\n                                columnNumber: 13\n                            }, this),\n                            !canUpload && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-blue-50 border border-blue-200 rounded-lg p-4 mb-6\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center space-x-3\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowDownTrayIcon_ArrowLeftIcon_ArrowRightIcon_BookOpenIcon_ChevronDownIcon_ChevronUpIcon_CloudArrowUpIcon_DocumentIcon_DocumentTextIcon_ExclamationTriangleIcon_EyeIcon_PlusIcon_ShieldCheckIcon_SpeakerWaveIcon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                            className: \"h-5 w-5 text-blue-600\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-2/page.tsx\",\n                                            lineNumber: 435,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                    className: \"font-gotham-rounded font-semibold text-blue-900 text-sm\",\n                                                    children: \"Mode View Only\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-2/page.tsx\",\n                                                    lineNumber: 437,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"font-gotham text-blue-800 text-xs\",\n                                                    children: \"Anda dapat melihat dan mendownload file, namun tidak dapat mengupload file baru.\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-2/page.tsx\",\n                                                    lineNumber: 440,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-2/page.tsx\",\n                                            lineNumber: 436,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-2/page.tsx\",\n                                    lineNumber: 434,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-2/page.tsx\",\n                                lineNumber: 433,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"font-gotham-rounded font-semibold text-gray-900 mb-3\",\n                                        children: \"File yang Tersedia\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-2/page.tsx\",\n                                        lineNumber: 450,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-2\",\n                                        children: [\n                                            currentSectionFiles.map((file)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"bg-white border border-gray-200 rounded-lg p-4 hover:bg-gray-50 transition-colors\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center justify-between\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center space-x-3\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowDownTrayIcon_ArrowLeftIcon_ArrowRightIcon_BookOpenIcon_ChevronDownIcon_ChevronUpIcon_CloudArrowUpIcon_DocumentIcon_DocumentTextIcon_ExclamationTriangleIcon_EyeIcon_PlusIcon_ShieldCheckIcon_SpeakerWaveIcon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                                        className: \"h-6 w-6 text-blue-600\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-2/page.tsx\",\n                                                                        lineNumber: 459,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                                                className: \"font-gotham font-medium text-gray-900 text-sm\",\n                                                                                children: file.name\n                                                                            }, void 0, false, {\n                                                                                fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-2/page.tsx\",\n                                                                                lineNumber: 461,\n                                                                                columnNumber: 25\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"flex items-center space-x-3 text-xs text-gray-500 font-gotham\",\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                        children: file.size\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-2/page.tsx\",\n                                                                                        lineNumber: 463,\n                                                                                        columnNumber: 27\n                                                                                    }, this),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                        children: \"•\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-2/page.tsx\",\n                                                                                        lineNumber: 464,\n                                                                                        columnNumber: 27\n                                                                                    }, this),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                        children: file.uploadDate\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-2/page.tsx\",\n                                                                                        lineNumber: 465,\n                                                                                        columnNumber: 27\n                                                                                    }, this),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                        children: \"•\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-2/page.tsx\",\n                                                                                        lineNumber: 466,\n                                                                                        columnNumber: 27\n                                                                                    }, this),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                        children: file.uploadedBy\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-2/page.tsx\",\n                                                                                        lineNumber: 467,\n                                                                                        columnNumber: 27\n                                                                                    }, this)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-2/page.tsx\",\n                                                                                lineNumber: 462,\n                                                                                columnNumber: 25\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-2/page.tsx\",\n                                                                        lineNumber: 460,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-2/page.tsx\",\n                                                                lineNumber: 458,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center space-x-1\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                        onClick: ()=>handleView(file),\n                                                                        className: \"p-2 text-gray-600 hover:text-blue-600 hover:bg-blue-50 rounded-lg transition-colors\",\n                                                                        title: \"View File\",\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowDownTrayIcon_ArrowLeftIcon_ArrowRightIcon_BookOpenIcon_ChevronDownIcon_ChevronUpIcon_CloudArrowUpIcon_DocumentIcon_DocumentTextIcon_ExclamationTriangleIcon_EyeIcon_PlusIcon_ShieldCheckIcon_SpeakerWaveIcon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                                            className: \"h-4 w-4\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-2/page.tsx\",\n                                                                            lineNumber: 478,\n                                                                            columnNumber: 25\n                                                                        }, this)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-2/page.tsx\",\n                                                                        lineNumber: 473,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                        onClick: ()=>handleDownload(file.name),\n                                                                        className: \"p-2 text-gray-600 hover:text-green-600 hover:bg-green-50 rounded-lg transition-colors\",\n                                                                        title: \"Download File\",\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowDownTrayIcon_ArrowLeftIcon_ArrowRightIcon_BookOpenIcon_ChevronDownIcon_ChevronUpIcon_CloudArrowUpIcon_DocumentIcon_DocumentTextIcon_ExclamationTriangleIcon_EyeIcon_PlusIcon_ShieldCheckIcon_SpeakerWaveIcon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                                            className: \"h-4 w-4\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-2/page.tsx\",\n                                                                            lineNumber: 485,\n                                                                            columnNumber: 25\n                                                                        }, this)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-2/page.tsx\",\n                                                                        lineNumber: 480,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    canUpload && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                        onClick: ()=>handleDelete(file.id),\n                                                                        className: \"p-2 text-gray-600 hover:text-red-600 hover:bg-red-50 rounded-lg transition-colors\",\n                                                                        title: \"Delete File\",\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowDownTrayIcon_ArrowLeftIcon_ArrowRightIcon_BookOpenIcon_ChevronDownIcon_ChevronUpIcon_CloudArrowUpIcon_DocumentIcon_DocumentTextIcon_ExclamationTriangleIcon_EyeIcon_PlusIcon_ShieldCheckIcon_SpeakerWaveIcon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                                            className: \"h-4 w-4\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-2/page.tsx\",\n                                                                            lineNumber: 495,\n                                                                            columnNumber: 27\n                                                                        }, this)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-2/page.tsx\",\n                                                                        lineNumber: 490,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-2/page.tsx\",\n                                                                lineNumber: 472,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-2/page.tsx\",\n                                                        lineNumber: 457,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                }, file.id, false, {\n                                                    fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-2/page.tsx\",\n                                                    lineNumber: 456,\n                                                    columnNumber: 17\n                                                }, this)),\n                                            currentSectionFiles.length === 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-center py-8\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowDownTrayIcon_ArrowLeftIcon_ArrowRightIcon_BookOpenIcon_ChevronDownIcon_ChevronUpIcon_CloudArrowUpIcon_DocumentIcon_DocumentTextIcon_ExclamationTriangleIcon_EyeIcon_PlusIcon_ShieldCheckIcon_SpeakerWaveIcon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                        className: \"h-12 w-12 text-gray-400 mx-auto mb-3\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-2/page.tsx\",\n                                                        lineNumber: 505,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"font-gotham text-gray-500 text-sm\",\n                                                        children: \"Belum ada file yang diupload untuk sub-dokumen ini\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-2/page.tsx\",\n                                                        lineNumber: 506,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"font-gotham text-gray-400 text-xs mt-1\",\n                                                        children: \"Maksimal 1 file per sub-dokumen\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-2/page.tsx\",\n                                                        lineNumber: 509,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-2/page.tsx\",\n                                                lineNumber: 504,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-2/page.tsx\",\n                                        lineNumber: 454,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-2/page.tsx\",\n                                lineNumber: 449,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-2/page.tsx\",\n                        lineNumber: 380,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-2/page.tsx\",\n                lineNumber: 361,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(FilePreviewModal, {\n                isOpen: showPreview,\n                onClose: ()=>setShowPreview(false),\n                fileName: previewFile.name,\n                fileType: previewFile.type,\n                fileContent: previewFile.content,\n                mimeType: previewFile.mimeType\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-2/page.tsx\",\n                lineNumber: 520,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-2/page.tsx\",\n        lineNumber: 360,\n        columnNumber: 5\n    }, this);\n}\n_s(FileManagerModal, \"WwKpo+R8DCEmGqCXleOmG/2pUVs=\");\n_c1 = FileManagerModal;\nfunction AddSectionModal(param) {\n    let { isOpen, onClose, onSave, subDocTitle } = param;\n    _s1();\n    const [title, setTitle] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [description, setDescription] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    if (!isOpen) return null;\n    const handleSubmit = (e)=>{\n        e.preventDefault();\n        if (title.trim()) {\n            onSave(title.trim(), description.trim());\n            setTitle('');\n            setDescription('');\n        }\n    };\n    const handleClose = ()=>{\n        setTitle('');\n        setDescription('');\n        onClose();\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"bg-white rounded-xl p-6 w-full max-w-md mx-4\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center justify-between mb-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                            className: \"font-gotham-rounded text-lg font-semibold text-gray-900\",\n                            children: \"Tambah Section Baru\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-2/page.tsx\",\n                            lineNumber: 565,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: handleClose,\n                            className: \"p-2 hover:bg-gray-100 rounded-lg transition-colors\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowDownTrayIcon_ArrowLeftIcon_ArrowRightIcon_BookOpenIcon_ChevronDownIcon_ChevronUpIcon_CloudArrowUpIcon_DocumentIcon_DocumentTextIcon_ExclamationTriangleIcon_EyeIcon_PlusIcon_ShieldCheckIcon_SpeakerWaveIcon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                className: \"h-5 w-5 text-gray-500\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-2/page.tsx\",\n                                lineNumber: 572,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-2/page.tsx\",\n                            lineNumber: 568,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-2/page.tsx\",\n                    lineNumber: 564,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                    className: \"font-gotham text-sm text-gray-600 mb-4\",\n                    children: [\n                        \"Menambah section baru untuk: \",\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            className: \"font-medium\",\n                            children: subDocTitle\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-2/page.tsx\",\n                            lineNumber: 577,\n                            columnNumber: 40\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-2/page.tsx\",\n                    lineNumber: 576,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                    onSubmit: handleSubmit,\n                    className: \"space-y-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                    className: \"block font-gotham text-sm font-medium text-gray-700 mb-2\",\n                                    children: \"Judul Section\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-2/page.tsx\",\n                                    lineNumber: 582,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                    type: \"text\",\n                                    value: title,\n                                    onChange: (e)=>setTitle(e.target.value),\n                                    className: \"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 font-gotham\",\n                                    placeholder: \"Masukkan judul section...\",\n                                    required: true\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-2/page.tsx\",\n                                    lineNumber: 585,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-2/page.tsx\",\n                            lineNumber: 581,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                    className: \"block font-gotham text-sm font-medium text-gray-700 mb-2\",\n                                    children: [\n                                        \"Deskripsi \",\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-gray-400 font-normal\",\n                                            children: \"(opsional)\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-2/page.tsx\",\n                                            lineNumber: 597,\n                                            columnNumber: 25\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-2/page.tsx\",\n                                    lineNumber: 596,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                                    value: description,\n                                    onChange: (e)=>setDescription(e.target.value),\n                                    rows: 3,\n                                    className: \"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 font-gotham\",\n                                    placeholder: \"Masukkan deskripsi section (opsional)...\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-2/page.tsx\",\n                                    lineNumber: 599,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-2/page.tsx\",\n                            lineNumber: 595,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex space-x-3 pt-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    type: \"button\",\n                                    onClick: handleClose,\n                                    className: \"flex-1 px-4 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors font-gotham font-medium\",\n                                    children: \"Batal\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-2/page.tsx\",\n                                    lineNumber: 609,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    type: \"submit\",\n                                    disabled: !title.trim(),\n                                    className: \"flex-1 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:bg-gray-300 disabled:cursor-not-allowed transition-colors font-gotham font-medium\",\n                                    children: \"Tambah Section\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-2/page.tsx\",\n                                    lineNumber: 616,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-2/page.tsx\",\n                            lineNumber: 608,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-2/page.tsx\",\n                    lineNumber: 580,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-2/page.tsx\",\n            lineNumber: 563,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-2/page.tsx\",\n        lineNumber: 562,\n        columnNumber: 5\n    }, this);\n}\n_s1(AddSectionModal, \"1UKQWTfo2RWmkwPNsekAdQbvqFk=\");\n_c2 = AddSectionModal;\nfunction AddSubSectionModal(param) {\n    let { isOpen, onClose, onSave, sectionTitle } = param;\n    _s2();\n    const [title, setTitle] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [description, setDescription] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [type, setType] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('upload');\n    const [formUrl, setFormUrl] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    if (!isOpen) return null;\n    const handleSubmit = (e)=>{\n        e.preventDefault();\n        if (title.trim() && (type === 'upload' || type === 'form' && formUrl.trim())) {\n            onSave(title.trim(), description.trim(), type, type === 'form' ? formUrl.trim() : undefined);\n            setTitle('');\n            setDescription('');\n            setType('upload');\n            setFormUrl('');\n        }\n    };\n    const handleClose = ()=>{\n        setTitle('');\n        setDescription('');\n        setType('upload');\n        setFormUrl('');\n        onClose();\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"bg-white rounded-xl p-6 w-full max-w-md mx-4\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center justify-between mb-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                            className: \"font-gotham-rounded text-lg font-semibold text-gray-900\",\n                            children: \"Tambah Sub-Section Baru\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-2/page.tsx\",\n                            lineNumber: 669,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: handleClose,\n                            className: \"p-2 hover:bg-gray-100 rounded-lg transition-colors\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowDownTrayIcon_ArrowLeftIcon_ArrowRightIcon_BookOpenIcon_ChevronDownIcon_ChevronUpIcon_CloudArrowUpIcon_DocumentIcon_DocumentTextIcon_ExclamationTriangleIcon_EyeIcon_PlusIcon_ShieldCheckIcon_SpeakerWaveIcon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                className: \"h-5 w-5 text-gray-500\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-2/page.tsx\",\n                                lineNumber: 676,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-2/page.tsx\",\n                            lineNumber: 672,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-2/page.tsx\",\n                    lineNumber: 668,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                    className: \"font-gotham text-sm text-gray-600 mb-4\",\n                    children: [\n                        \"Menambah sub-section baru untuk: \",\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            className: \"font-medium\",\n                            children: sectionTitle\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-2/page.tsx\",\n                            lineNumber: 681,\n                            columnNumber: 44\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-2/page.tsx\",\n                    lineNumber: 680,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                    onSubmit: handleSubmit,\n                    className: \"space-y-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                    className: \"block font-gotham text-sm font-medium text-gray-700 mb-2\",\n                                    children: \"Judul Sub-Section\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-2/page.tsx\",\n                                    lineNumber: 686,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                    type: \"text\",\n                                    value: title,\n                                    onChange: (e)=>setTitle(e.target.value),\n                                    className: \"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 font-gotham\",\n                                    placeholder: \"Masukkan judul sub-section...\",\n                                    required: true\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-2/page.tsx\",\n                                    lineNumber: 689,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-2/page.tsx\",\n                            lineNumber: 685,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                    className: \"block font-gotham text-sm font-medium text-gray-700 mb-2\",\n                                    children: [\n                                        \"Deskripsi \",\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-gray-400 font-normal\",\n                                            children: \"(opsional)\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-2/page.tsx\",\n                                            lineNumber: 701,\n                                            columnNumber: 25\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-2/page.tsx\",\n                                    lineNumber: 700,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                                    value: description,\n                                    onChange: (e)=>setDescription(e.target.value),\n                                    rows: 3,\n                                    className: \"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 font-gotham\",\n                                    placeholder: \"Masukkan deskripsi sub-section (opsional)...\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-2/page.tsx\",\n                                    lineNumber: 703,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-2/page.tsx\",\n                            lineNumber: 699,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                    className: \"block font-gotham text-sm font-medium text-gray-700 mb-2\",\n                                    children: \"Tipe Sub-Section\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-2/page.tsx\",\n                                    lineNumber: 713,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"space-y-3\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            className: \"flex items-center p-3 border border-gray-200 rounded-lg hover:bg-gray-50 cursor-pointer\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                    type: \"radio\",\n                                                    name: \"subsectionType\",\n                                                    value: \"upload\",\n                                                    checked: type === 'upload',\n                                                    onChange: (e)=>setType(e.target.value),\n                                                    className: \"mr-3\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-2/page.tsx\",\n                                                    lineNumber: 718,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center space-x-3\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"p-2 bg-blue-100 text-blue-600 rounded-lg\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowDownTrayIcon_ArrowLeftIcon_ArrowRightIcon_BookOpenIcon_ChevronDownIcon_ChevronUpIcon_CloudArrowUpIcon_DocumentIcon_DocumentTextIcon_ExclamationTriangleIcon_EyeIcon_PlusIcon_ShieldCheckIcon_SpeakerWaveIcon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                                className: \"h-5 w-5\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-2/page.tsx\",\n                                                                lineNumber: 728,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-2/page.tsx\",\n                                                            lineNumber: 727,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"font-gotham font-medium text-gray-900\",\n                                                                    children: \"Upload Dokumen\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-2/page.tsx\",\n                                                                    lineNumber: 731,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"font-gotham text-sm text-gray-600\",\n                                                                    children: \"Sub-section untuk upload file dokumen\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-2/page.tsx\",\n                                                                    lineNumber: 732,\n                                                                    columnNumber: 21\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-2/page.tsx\",\n                                                            lineNumber: 730,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-2/page.tsx\",\n                                                    lineNumber: 726,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-2/page.tsx\",\n                                            lineNumber: 717,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            className: \"flex items-center p-3 border border-gray-200 rounded-lg hover:bg-gray-50 cursor-pointer\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                    type: \"radio\",\n                                                    name: \"subsectionType\",\n                                                    value: \"form\",\n                                                    checked: type === 'form',\n                                                    onChange: (e)=>setType(e.target.value),\n                                                    className: \"mr-3\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-2/page.tsx\",\n                                                    lineNumber: 738,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center space-x-3\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"p-2 bg-green-100 text-green-600 rounded-lg\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowDownTrayIcon_ArrowLeftIcon_ArrowRightIcon_BookOpenIcon_ChevronDownIcon_ChevronUpIcon_CloudArrowUpIcon_DocumentIcon_DocumentTextIcon_ExclamationTriangleIcon_EyeIcon_PlusIcon_ShieldCheckIcon_SpeakerWaveIcon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                                className: \"h-5 w-5\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-2/page.tsx\",\n                                                                lineNumber: 748,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-2/page.tsx\",\n                                                            lineNumber: 747,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"font-gotham font-medium text-gray-900\",\n                                                                    children: \"Isi Formulir\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-2/page.tsx\",\n                                                                    lineNumber: 751,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"font-gotham text-sm text-gray-600\",\n                                                                    children: \"Sub-section untuk mengisi formulir online\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-2/page.tsx\",\n                                                                    lineNumber: 752,\n                                                                    columnNumber: 21\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-2/page.tsx\",\n                                                            lineNumber: 750,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-2/page.tsx\",\n                                                    lineNumber: 746,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-2/page.tsx\",\n                                            lineNumber: 737,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-2/page.tsx\",\n                                    lineNumber: 716,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-2/page.tsx\",\n                            lineNumber: 712,\n                            columnNumber: 11\n                        }, this),\n                        type === 'form' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                    className: \"block font-gotham text-sm font-medium text-gray-700 mb-2\",\n                                    children: \"URL Formulir\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-2/page.tsx\",\n                                    lineNumber: 761,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                    type: \"url\",\n                                    value: formUrl,\n                                    onChange: (e)=>setFormUrl(e.target.value),\n                                    className: \"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 font-gotham\",\n                                    placeholder: \"Contoh: /forms/risk-assessment\",\n                                    required: true\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-2/page.tsx\",\n                                    lineNumber: 764,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"font-gotham text-xs text-gray-500 mt-1\",\n                                    children: \"Masukkan URL relatif atau absolut untuk formulir\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-2/page.tsx\",\n                                    lineNumber: 772,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-2/page.tsx\",\n                            lineNumber: 760,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex space-x-3 pt-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    type: \"button\",\n                                    onClick: handleClose,\n                                    className: \"flex-1 px-4 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors font-gotham font-medium\",\n                                    children: \"Batal\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-2/page.tsx\",\n                                    lineNumber: 779,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    type: \"submit\",\n                                    disabled: !title.trim() || type === 'form' && !formUrl.trim(),\n                                    className: \"flex-1 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:bg-gray-300 disabled:cursor-not-allowed transition-colors font-gotham font-medium\",\n                                    children: \"Tambah Sub-Section\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-2/page.tsx\",\n                                    lineNumber: 786,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-2/page.tsx\",\n                            lineNumber: 778,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-2/page.tsx\",\n                    lineNumber: 684,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-2/page.tsx\",\n            lineNumber: 667,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-2/page.tsx\",\n        lineNumber: 666,\n        columnNumber: 5\n    }, this);\n}\n_s2(AddSubSectionModal, \"j4muNlo4jrHMXOkSO9GEWhR03Ec=\");\n_c3 = AddSubSectionModal;\nfunction DeleteConfirmModal(param) {\n    let { isOpen, onClose, onConfirm, title, type } = param;\n    if (!isOpen) return null;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"bg-white rounded-xl p-6 w-full max-w-md mx-4\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center justify-between mb-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                            className: \"font-gotham-rounded text-lg font-semibold text-red-900\",\n                            children: [\n                                \"Hapus \",\n                                type === 'section' ? 'Section' : 'Sub-Section'\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-2/page.tsx\",\n                            lineNumber: 816,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: onClose,\n                            className: \"p-2 hover:bg-gray-100 rounded-lg transition-colors\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowDownTrayIcon_ArrowLeftIcon_ArrowRightIcon_BookOpenIcon_ChevronDownIcon_ChevronUpIcon_CloudArrowUpIcon_DocumentIcon_DocumentTextIcon_ExclamationTriangleIcon_EyeIcon_PlusIcon_ShieldCheckIcon_SpeakerWaveIcon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                className: \"h-5 w-5 text-gray-500\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-2/page.tsx\",\n                                lineNumber: 823,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-2/page.tsx\",\n                            lineNumber: 819,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-2/page.tsx\",\n                    lineNumber: 815,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"mb-6\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center space-x-3 mb-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"p-3 bg-red-100 rounded-lg\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowDownTrayIcon_ArrowLeftIcon_ArrowRightIcon_BookOpenIcon_ChevronDownIcon_ChevronUpIcon_CloudArrowUpIcon_DocumentIcon_DocumentTextIcon_ExclamationTriangleIcon_EyeIcon_PlusIcon_ShieldCheckIcon_SpeakerWaveIcon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                        className: \"h-6 w-6 text-red-600\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-2/page.tsx\",\n                                        lineNumber: 830,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-2/page.tsx\",\n                                    lineNumber: 829,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"font-gotham font-medium text-gray-900\",\n                                            children: [\n                                                \"Apakah Anda yakin ingin menghapus \",\n                                                type === 'section' ? 'section' : 'sub-section',\n                                                \" ini?\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-2/page.tsx\",\n                                            lineNumber: 833,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"font-gotham text-sm text-gray-600 mt-1\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"font-medium\",\n                                                children: [\n                                                    '\"',\n                                                    title,\n                                                    '\"'\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-2/page.tsx\",\n                                                lineNumber: 837,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-2/page.tsx\",\n                                            lineNumber: 836,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-2/page.tsx\",\n                                    lineNumber: 832,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-2/page.tsx\",\n                            lineNumber: 828,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-red-50 border border-red-200 rounded-lg p-3\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"font-gotham text-sm text-red-800\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"font-medium\",\n                                        children: \"Peringatan:\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-2/page.tsx\",\n                                        lineNumber: 844,\n                                        columnNumber: 15\n                                    }, this),\n                                    \" Tindakan ini tidak dapat dibatalkan.\",\n                                    type === 'section' ? ' Semua sub-section dan file yang terkait akan ikut terhapus.' : ' File yang terkait dengan sub-section ini akan ikut terhapus.'\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-2/page.tsx\",\n                                lineNumber: 843,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-2/page.tsx\",\n                            lineNumber: 842,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-2/page.tsx\",\n                    lineNumber: 827,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex space-x-3\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: onClose,\n                            className: \"flex-1 px-4 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors font-gotham font-medium\",\n                            children: \"Batal\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-2/page.tsx\",\n                            lineNumber: 854,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: onConfirm,\n                            className: \"flex-1 px-4 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700 transition-colors font-gotham font-medium\",\n                            children: \"Hapus\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-2/page.tsx\",\n                            lineNumber: 860,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-2/page.tsx\",\n                    lineNumber: 853,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-2/page.tsx\",\n            lineNumber: 814,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-2/page.tsx\",\n        lineNumber: 813,\n        columnNumber: 5\n    }, this);\n}\n_c4 = DeleteConfirmModal;\nfunction PedomanPeraturanPage() {\n    _s3();\n    const { user } = (0,_store_auth__WEBPACK_IMPORTED_MODULE_2__.useAuth)();\n    const { dynamicSections: dynamicMainSections, dynamicSubSections, updateDynamicSections: updateDynamicMainSections, updateDynamicSubSections } = (0,_contexts_ProgramContext__WEBPACK_IMPORTED_MODULE_3__.useProgramDocument)('doc2');\n    const [expandedSection, setExpandedSection] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [showFileManager, setShowFileManager] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [currentSection, setCurrentSection] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [showAddSection, setShowAddSection] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [showAddSubSection, setShowAddSubSection] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [showDeleteConfirm, setShowDeleteConfirm] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [currentSubDocId, setCurrentSubDocId] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [deleteTarget, setDeleteTarget] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        type: 'section',\n        id: '',\n        title: ''\n    });\n    if (!user) return null;\n    const handleSectionToggle = (sectionId)=>{\n        setExpandedSection(expandedSection === sectionId ? null : sectionId);\n    };\n    const handleSubSectionToggle = (subSectionId)=>{\n        setExpandedSection(expandedSection === subSectionId ? null : subSectionId);\n    };\n    const handleUpload = (subSectionId)=>{\n        setCurrentSection(subSectionId);\n        setShowFileManager(true);\n    };\n    const handleViewFile = (subSectionId)=>{\n        // Get files from localStorage for this section\n        const savedFiles = localStorage.getItem('doc-1-section-files');\n        const sectionFiles = savedFiles ? JSON.parse(savedFiles) : {};\n        const filesForSection = sectionFiles[subSectionId] || [];\n        if (filesForSection.length === 0) {\n            alert('Belum ada file yang diupload untuk sub-dokumen ini.');\n            return;\n        }\n        // If there's a file, show it in the file manager\n        setCurrentSection(subSectionId);\n        setShowFileManager(true);\n    };\n    // Handle adding new main section (E, F, G, etc.)\n    const handleAddMainSection = ()=>{\n        setShowAddSection(true);\n    };\n    // Handle adding new sub-section (A.4, B.1, etc.)\n    const handleAddSubSection = (subDocId)=>{\n        setCurrentSubDocId(subDocId);\n        setShowAddSubSection(true);\n    };\n    // Save new main section\n    const handleSaveNewMainSection = (title, description)=>{\n        const newSectionId = \"dynamic-section-\".concat(Date.now());\n        const newSection = {\n            id: newSectionId,\n            title: title,\n            description: description,\n            iconType: 'DocumentTextIcon',\n            color: 'bg-purple-100 text-purple-600',\n            hoverColor: 'hover:border-purple-500 hover:bg-purple-50',\n            iconHoverColor: 'hover:text-purple-600',\n            subSections: [],\n            createdAt: new Date().toISOString(),\n            createdBy: user.name\n        };\n        const updatedSections = [\n            ...dynamicMainSections,\n            newSection\n        ];\n        updateDynamicMainSections(updatedSections);\n        setShowAddSection(false);\n    };\n    // Save new sub-section\n    const handleSaveNewSubSection = (title, description, type, formUrl)=>{\n        const subDocSubSections = dynamicSubSections[currentSubDocId] || [];\n        const newSubSectionId = \"\".concat(currentSubDocId, \"-subsection-\").concat(Date.now());\n        const newSubSection = {\n            id: newSubSectionId,\n            title: title,\n            description: description,\n            type: type,\n            formUrl: type === 'form' ? formUrl : undefined,\n            createdAt: new Date().toISOString(),\n            createdBy: user.name\n        };\n        const updatedSubSections = {\n            ...dynamicSubSections,\n            [currentSubDocId]: [\n                ...subDocSubSections,\n                newSubSection\n            ]\n        };\n        updateDynamicSubSections(updatedSubSections);\n        setShowAddSubSection(false);\n        setCurrentSubDocId('');\n    };\n    // Handle delete section/sub-section\n    const handleDeleteRequest = (type, id, title)=>{\n        setDeleteTarget({\n            type,\n            id,\n            title\n        });\n        setShowDeleteConfirm(true);\n    };\n    const handleConfirmDelete = ()=>{\n        if (deleteTarget.type === 'section') {\n            // Delete main section\n            const updatedSections = dynamicMainSections.filter((section)=>section.id !== deleteTarget.id);\n            updateDynamicMainSections(updatedSections);\n            // Also delete all sub-sections for this section\n            const updatedSubSections = {\n                ...dynamicSubSections\n            };\n            delete updatedSubSections[deleteTarget.id];\n            updateDynamicSubSections(updatedSubSections);\n            // Delete all files for this section and its sub-sections\n            cleanupSectionFiles(deleteTarget.id);\n        } else if (deleteTarget.type === 'subsection') {\n            // Delete sub-section\n            const sectionId = findSectionIdForSubSection(deleteTarget.id);\n            if (sectionId) {\n                const updatedSubSections = {\n                    ...dynamicSubSections,\n                    [sectionId]: dynamicSubSections[sectionId].filter((subSection)=>subSection.id !== deleteTarget.id)\n                };\n                updateDynamicSubSections(updatedSubSections);\n                // Delete files for this sub-section\n                cleanupSubSectionFiles(deleteTarget.id);\n            }\n        }\n        setShowDeleteConfirm(false);\n        setDeleteTarget({\n            type: 'section',\n            id: '',\n            title: ''\n        });\n    };\n    // Helper function to find section ID for a sub-section\n    const findSectionIdForSubSection = (subSectionId)=>{\n        for(const sectionId in dynamicSubSections){\n            if (dynamicSubSections[sectionId].some((subSection)=>subSection.id === subSectionId)) {\n                return sectionId;\n            }\n        }\n        return null;\n    };\n    // Cleanup files when deleting section/sub-section\n    const cleanupSectionFiles = (sectionId)=>{\n        try {\n            const savedFiles = localStorage.getItem('doc-1-section-files');\n            if (savedFiles) {\n                const sectionFiles = JSON.parse(savedFiles);\n                // Remove files for the section itself\n                delete sectionFiles[sectionId];\n                // Remove files for all sub-sections of this section\n                const subSections = dynamicSubSections[sectionId] || [];\n                subSections.forEach((subSection)=>{\n                    delete sectionFiles[subSection.id];\n                });\n                localStorage.setItem('doc-1-section-files', JSON.stringify(sectionFiles));\n            }\n        } catch (error) {\n            console.error('Error cleaning up section files:', error);\n        }\n    };\n    const cleanupSubSectionFiles = (subSectionId)=>{\n        try {\n            const savedFiles = localStorage.getItem('doc-1-section-files');\n            if (savedFiles) {\n                const sectionFiles = JSON.parse(savedFiles);\n                delete sectionFiles[subSectionId];\n                localStorage.setItem('doc-1-section-files', JSON.stringify(sectionFiles));\n            }\n        } catch (error) {\n            console.error('Error cleaning up sub-section files:', error);\n        }\n    };\n    // Get icon component from string identifier\n    const getIconComponent = (iconType)=>{\n        const iconMap = {\n            'DocumentTextIcon': _barrel_optimize_names_ArrowDownTrayIcon_ArrowLeftIcon_ArrowRightIcon_BookOpenIcon_ChevronDownIcon_ChevronUpIcon_CloudArrowUpIcon_DocumentIcon_DocumentTextIcon_ExclamationTriangleIcon_EyeIcon_PlusIcon_ShieldCheckIcon_SpeakerWaveIcon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_11__[\"default\"],\n            'ShieldCheckIcon': _barrel_optimize_names_ArrowDownTrayIcon_ArrowLeftIcon_ArrowRightIcon_BookOpenIcon_ChevronDownIcon_ChevronUpIcon_CloudArrowUpIcon_DocumentIcon_DocumentTextIcon_ExclamationTriangleIcon_EyeIcon_PlusIcon_ShieldCheckIcon_SpeakerWaveIcon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_12__[\"default\"],\n            'BookOpenIcon': _barrel_optimize_names_ArrowDownTrayIcon_ArrowLeftIcon_ArrowRightIcon_BookOpenIcon_ChevronDownIcon_ChevronUpIcon_CloudArrowUpIcon_DocumentIcon_DocumentTextIcon_ExclamationTriangleIcon_EyeIcon_PlusIcon_ShieldCheckIcon_SpeakerWaveIcon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_13__[\"default\"],\n            'ExclamationTriangleIcon': _barrel_optimize_names_ArrowDownTrayIcon_ArrowLeftIcon_ArrowRightIcon_BookOpenIcon_ChevronDownIcon_ChevronUpIcon_CloudArrowUpIcon_DocumentIcon_DocumentTextIcon_ExclamationTriangleIcon_EyeIcon_PlusIcon_ShieldCheckIcon_SpeakerWaveIcon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_14__[\"default\"],\n            'SpeakerWaveIcon': _barrel_optimize_names_ArrowDownTrayIcon_ArrowLeftIcon_ArrowRightIcon_BookOpenIcon_ChevronDownIcon_ChevronUpIcon_CloudArrowUpIcon_DocumentIcon_DocumentTextIcon_ExclamationTriangleIcon_EyeIcon_PlusIcon_ShieldCheckIcon_SpeakerWaveIcon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_15__[\"default\"]\n        };\n        return iconMap[iconType] || _barrel_optimize_names_ArrowDownTrayIcon_ArrowLeftIcon_ArrowRightIcon_BookOpenIcon_ChevronDownIcon_ChevronUpIcon_CloudArrowUpIcon_DocumentIcon_DocumentTextIcon_ExclamationTriangleIcon_EyeIcon_PlusIcon_ShieldCheckIcon_SpeakerWaveIcon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_11__[\"default\"];\n    };\n    // Get all sections (static + dynamic)\n    const getAllSections = ()=>{\n        // Add icon component and ensure all required properties for dynamic sections\n        const dynamicSectionsWithIcons = dynamicMainSections.map((section)=>({\n                ...section,\n                icon: getIconComponent(section.iconType || 'DocumentTextIcon'),\n                subSections: section.subSections || [],\n                color: section.color || 'bg-purple-100 text-purple-600',\n                hoverColor: section.hoverColor || 'hover:border-purple-500 hover:bg-purple-50',\n                iconHoverColor: section.iconHoverColor || 'hover:text-purple-600'\n            }));\n        return [\n            ...SUB_DOCUMENTS,\n            ...dynamicSectionsWithIcons\n        ];\n    };\n    // Get all sub-sections for a section (static + dynamic)\n    const getAllSubSections = (sectionId)=>{\n        const staticSection = SUB_DOCUMENTS.find((doc)=>doc.id === sectionId);\n        const staticSubSections = staticSection ? staticSection.subSections : [];\n        const dynamicSubSectionsForSection = dynamicSubSections[sectionId] || [];\n        return [\n            ...staticSubSections,\n            ...dynamicSubSectionsForSection\n        ];\n    };\n    // Check if section can be deleted (only dynamic sections)\n    const canDeleteSection = (sectionId)=>{\n        return dynamicMainSections.some((section)=>section.id === sectionId);\n    };\n    // Check if sub-section can be deleted (only dynamic sub-sections)\n    const canDeleteSubSection = (subSectionId)=>{\n        for(const sectionId in dynamicSubSections){\n            if (dynamicSubSections[sectionId].some((subSection)=>subSection.id === subSectionId)) {\n                return true;\n            }\n        }\n        return false;\n    };\n    const handleBack = ()=>{\n        window.history.back();\n    };\n    const getSectionTitle = (sectionId)=>{\n        const sectionTitles = {\n            'manual-smap': 'Manual SMAP',\n            'struktur-organisasi': 'Struktur Organisasi SMAP Telkom',\n            'kebijakan-anti-suap': 'Kebijakan Anti Suap Telkom',\n            'formulir-risk-assessment': 'Formulir Penilaian Risiko Penyuapan (Risk Assessment)',\n            'sub-doc-2': 'Code of Conduct',\n            'sub-doc-3': 'Conflict of Interest',\n            'sub-doc-4': 'Whistle Blower System'\n        };\n        // Check if it's a dynamic main section\n        const dynamicMainSection = dynamicMainSections.find((section)=>section.id === sectionId);\n        if (dynamicMainSection) {\n            return dynamicMainSection.title;\n        }\n        // Check if it's a dynamic sub-section\n        for(const subDocId in dynamicSubSections){\n            const subSections = dynamicSubSections[subDocId];\n            const dynamicSubSection = subSections.find((subSection)=>subSection.id === sectionId);\n            if (dynamicSubSection) {\n                return dynamicSubSection.title;\n            }\n        }\n        return sectionTitles[sectionId] || sectionId;\n    };\n    const hasFileInSection = (sectionId)=>{\n        const savedFiles = localStorage.getItem('doc-1-section-files');\n        const sectionFiles = savedFiles ? JSON.parse(savedFiles) : {};\n        const filesForSection = sectionFiles[sectionId] || [];\n        return filesForSection.length > 0;\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_layout_AppLayout__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"container mx-auto px-4 py-8\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"max-w-6xl mx-auto\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"mb-8\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center space-x-3 mb-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"p-3 bg-red-100 rounded-xl\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowDownTrayIcon_ArrowLeftIcon_ArrowRightIcon_BookOpenIcon_ChevronDownIcon_ChevronUpIcon_CloudArrowUpIcon_DocumentIcon_DocumentTextIcon_ExclamationTriangleIcon_EyeIcon_PlusIcon_ShieldCheckIcon_SpeakerWaveIcon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                className: \"h-8 w-8 text-red-600\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-2/page.tsx\",\n                                                lineNumber: 1169,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-2/page.tsx\",\n                                            lineNumber: 1168,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                                    className: \"font-gotham-rounded text-3xl font-bold text-gray-900\",\n                                                    children: \"Prosedur dan Instruksi Kerja\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-2/page.tsx\",\n                                                    lineNumber: 1172,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"font-gotham text-gray-600 mt-1\",\n                                                    children: \"Prosedur operasional standar dan instruksi kerja\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-2/page.tsx\",\n                                                    lineNumber: 1175,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-2/page.tsx\",\n                                            lineNumber: 1171,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-2/page.tsx\",\n                                    lineNumber: 1167,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                                    className: \"flex items-center space-x-2 text-sm text-gray-500 font-gotham mb-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                            href: \"/dashboard\",\n                                            className: \"hover:text-red-600 transition-colors\",\n                                            children: \"Dashboard\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-2/page.tsx\",\n                                            lineNumber: 1183,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            children: \"›\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-2/page.tsx\",\n                                            lineNumber: 1186,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-gray-900\",\n                                            children: \"Prosedur dan Instruksi Kerja\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-2/page.tsx\",\n                                            lineNumber: 1187,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-2/page.tsx\",\n                                    lineNumber: 1182,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: handleBack,\n                                    className: \"flex items-center space-x-2 px-4 py-2 bg-gray-100 text-gray-700 rounded-lg hover:bg-gray-200 transition-colors font-gotham font-medium\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowDownTrayIcon_ArrowLeftIcon_ArrowRightIcon_BookOpenIcon_ChevronDownIcon_ChevronUpIcon_CloudArrowUpIcon_DocumentIcon_DocumentTextIcon_ExclamationTriangleIcon_EyeIcon_PlusIcon_ShieldCheckIcon_SpeakerWaveIcon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                            className: \"h-4 w-4\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-2/page.tsx\",\n                                            lineNumber: 1195,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            children: \"Kembali\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-2/page.tsx\",\n                                            lineNumber: 1196,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-2/page.tsx\",\n                                    lineNumber: 1191,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-2/page.tsx\",\n                            lineNumber: 1166,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-6\",\n                            children: [\n                                getAllSections().map((subDoc, index)=>{\n                                    const IconComponent = subDoc.icon;\n                                    const isExpanded = expandedSection === subDoc.id;\n                                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"border border-gray-200 rounded-xl\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"p-6 cursor-pointer hover:bg-gray-50 transition-all duration-200 \".concat(subDoc.hoverColor),\n                                                onClick: ()=>handleSectionToggle(subDoc.id),\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-start justify-between\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-start space-x-4\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"p-3 rounded-xl \".concat(subDoc.color),\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(IconComponent, {\n                                                                        className: \"h-6 w-6\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-2/page.tsx\",\n                                                                        lineNumber: 1219,\n                                                                        columnNumber: 27\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-2/page.tsx\",\n                                                                    lineNumber: 1218,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex-1\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                            className: \"font-gotham-rounded text-lg font-semibold text-gray-900 mb-2\",\n                                                                            children: [\n                                                                                String.fromCharCode(65 + index),\n                                                                                \". \",\n                                                                                subDoc.title\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-2/page.tsx\",\n                                                                            lineNumber: 1222,\n                                                                            columnNumber: 27\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                            className: \"font-gotham text-gray-600 text-sm leading-relaxed\",\n                                                                            children: subDoc.description\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-2/page.tsx\",\n                                                                            lineNumber: 1225,\n                                                                            columnNumber: 27\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-2/page.tsx\",\n                                                                    lineNumber: 1221,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-2/page.tsx\",\n                                                            lineNumber: 1217,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center space-x-2\",\n                                                            children: [\n                                                                canDeleteSection(subDoc.id) && (user.role === 'admin_super' || user.role === 'admin_biasa') && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                    onClick: (e)=>{\n                                                                        e.stopPropagation(); // Prevent section toggle\n                                                                        handleDeleteRequest('section', subDoc.id, subDoc.title);\n                                                                    },\n                                                                    className: \"p-2 border border-red-300 rounded-lg hover:bg-red-50 hover:border-red-400 transition-all duration-200 group\",\n                                                                    title: \"Hapus Section\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowDownTrayIcon_ArrowLeftIcon_ArrowRightIcon_BookOpenIcon_ChevronDownIcon_ChevronUpIcon_CloudArrowUpIcon_DocumentIcon_DocumentTextIcon_ExclamationTriangleIcon_EyeIcon_PlusIcon_ShieldCheckIcon_SpeakerWaveIcon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                                        className: \"h-4 w-4 text-red-500 group-hover:text-red-600\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-2/page.tsx\",\n                                                                        lineNumber: 1241,\n                                                                        columnNumber: 29\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-2/page.tsx\",\n                                                                    lineNumber: 1233,\n                                                                    columnNumber: 27\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"p-2 border border-gray-300 rounded-lg transition-all duration-200 \".concat(subDoc.hoverColor),\n                                                                    children: isExpanded ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowDownTrayIcon_ArrowLeftIcon_ArrowRightIcon_BookOpenIcon_ChevronDownIcon_ChevronUpIcon_CloudArrowUpIcon_DocumentIcon_DocumentTextIcon_ExclamationTriangleIcon_EyeIcon_PlusIcon_ShieldCheckIcon_SpeakerWaveIcon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                                        className: \"h-5 w-5 text-gray-600 transition-colors duration-200 \".concat(subDoc.iconHoverColor)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-2/page.tsx\",\n                                                                        lineNumber: 1248,\n                                                                        columnNumber: 29\n                                                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowDownTrayIcon_ArrowLeftIcon_ArrowRightIcon_BookOpenIcon_ChevronDownIcon_ChevronUpIcon_CloudArrowUpIcon_DocumentIcon_DocumentTextIcon_ExclamationTriangleIcon_EyeIcon_PlusIcon_ShieldCheckIcon_SpeakerWaveIcon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                                        className: \"h-5 w-5 text-gray-600 transition-colors duration-200 \".concat(subDoc.iconHoverColor)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-2/page.tsx\",\n                                                                        lineNumber: 1250,\n                                                                        columnNumber: 29\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-2/page.tsx\",\n                                                                    lineNumber: 1246,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-2/page.tsx\",\n                                                            lineNumber: 1230,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-2/page.tsx\",\n                                                    lineNumber: 1216,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-2/page.tsx\",\n                                                lineNumber: 1212,\n                                                columnNumber: 19\n                                            }, this),\n                                            isExpanded && (getAllSubSections(subDoc.id).length > 0 || user.role === 'admin_super' || user.role === 'admin_biasa') && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"border-t border-gray-200 bg-gray-50\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"p-6\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"space-y-4\",\n                                                        children: [\n                                                            getAllSubSections(subDoc.id).map((subSection, subIndex)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"bg-white border border-gray-200 rounded-lg p-4\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"flex items-center justify-between\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"flex-1\",\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                        className: \"flex items-center space-x-2 mb-2\",\n                                                                                        children: [\n                                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                                className: \"p-1 rounded \".concat(subSection.type === 'form' ? 'bg-green-100 text-green-600' : 'bg-blue-100 text-blue-600'),\n                                                                                                children: subSection.type === 'form' ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowDownTrayIcon_ArrowLeftIcon_ArrowRightIcon_BookOpenIcon_ChevronDownIcon_ChevronUpIcon_CloudArrowUpIcon_DocumentIcon_DocumentTextIcon_ExclamationTriangleIcon_EyeIcon_PlusIcon_ShieldCheckIcon_SpeakerWaveIcon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                                                                    className: \"h-3 w-3\"\n                                                                                                }, void 0, false, {\n                                                                                                    fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-2/page.tsx\",\n                                                                                                    lineNumber: 1276,\n                                                                                                    columnNumber: 41\n                                                                                                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowDownTrayIcon_ArrowLeftIcon_ArrowRightIcon_BookOpenIcon_ChevronDownIcon_ChevronUpIcon_CloudArrowUpIcon_DocumentIcon_DocumentTextIcon_ExclamationTriangleIcon_EyeIcon_PlusIcon_ShieldCheckIcon_SpeakerWaveIcon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                                                                    className: \"h-3 w-3\"\n                                                                                                }, void 0, false, {\n                                                                                                    fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-2/page.tsx\",\n                                                                                                    lineNumber: 1278,\n                                                                                                    columnNumber: 41\n                                                                                                }, this)\n                                                                                            }, void 0, false, {\n                                                                                                fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-2/page.tsx\",\n                                                                                                lineNumber: 1270,\n                                                                                                columnNumber: 37\n                                                                                            }, this),\n                                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                                                                className: \"font-gotham-rounded text-base font-semibold text-gray-900\",\n                                                                                                children: [\n                                                                                                    String.fromCharCode(65 + index),\n                                                                                                    \".\",\n                                                                                                    subIndex + 1,\n                                                                                                    \" \",\n                                                                                                    subSection.title\n                                                                                                ]\n                                                                                            }, void 0, true, {\n                                                                                                fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-2/page.tsx\",\n                                                                                                lineNumber: 1281,\n                                                                                                columnNumber: 37\n                                                                                            }, this),\n                                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                                className: \"px-2 py-0.5 text-xs font-medium rounded-full \".concat(subSection.type === 'form' ? 'bg-green-100 text-green-800' : 'bg-blue-100 text-blue-800'),\n                                                                                                children: subSection.type === 'form' ? 'Form' : 'Upload'\n                                                                                            }, void 0, false, {\n                                                                                                fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-2/page.tsx\",\n                                                                                                lineNumber: 1284,\n                                                                                                columnNumber: 37\n                                                                                            }, this)\n                                                                                        ]\n                                                                                    }, void 0, true, {\n                                                                                        fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-2/page.tsx\",\n                                                                                        lineNumber: 1269,\n                                                                                        columnNumber: 35\n                                                                                    }, this),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                        className: \"font-gotham text-gray-600 text-sm\",\n                                                                                        children: subSection.description\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-2/page.tsx\",\n                                                                                        lineNumber: 1292,\n                                                                                        columnNumber: 35\n                                                                                    }, this),\n                                                                                    subSection.type === 'form' && subSection.formUrl && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                        className: \"font-gotham text-xs text-blue-600 mt-1\",\n                                                                                        children: [\n                                                                                            \"URL: \",\n                                                                                            subSection.formUrl\n                                                                                        ]\n                                                                                    }, void 0, true, {\n                                                                                        fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-2/page.tsx\",\n                                                                                        lineNumber: 1296,\n                                                                                        columnNumber: 37\n                                                                                    }, this)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-2/page.tsx\",\n                                                                                lineNumber: 1268,\n                                                                                columnNumber: 33\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"flex items-center space-x-2 ml-4\",\n                                                                                children: [\n                                                                                    subSection.type === 'form' ? /* Form button - for form type subsections */ /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                                        onClick: ()=>window.open(subSection.formUrl, '_blank'),\n                                                                                        className: \"flex items-center space-x-2 px-3 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors text-sm font-gotham font-medium\",\n                                                                                        children: [\n                                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowDownTrayIcon_ArrowLeftIcon_ArrowRightIcon_BookOpenIcon_ChevronDownIcon_ChevronUpIcon_CloudArrowUpIcon_DocumentIcon_DocumentTextIcon_ExclamationTriangleIcon_EyeIcon_PlusIcon_ShieldCheckIcon_SpeakerWaveIcon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                                                                className: \"h-4 w-4\"\n                                                                                            }, void 0, false, {\n                                                                                                fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-2/page.tsx\",\n                                                                                                lineNumber: 1308,\n                                                                                                columnNumber: 39\n                                                                                            }, this),\n                                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                                children: \"Isi Form\"\n                                                                                            }, void 0, false, {\n                                                                                                fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-2/page.tsx\",\n                                                                                                lineNumber: 1309,\n                                                                                                columnNumber: 39\n                                                                                            }, this)\n                                                                                        ]\n                                                                                    }, void 0, true, {\n                                                                                        fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-2/page.tsx\",\n                                                                                        lineNumber: 1304,\n                                                                                        columnNumber: 37\n                                                                                    }, this) : /* Upload and View buttons - for upload type subsections */ /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                                                        children: [\n                                                                                            (user.role === 'admin_super' || user.role === 'admin_biasa' || user.role === 'scoopers') && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                                                onClick: ()=>handleUpload(subSection.id),\n                                                                                                className: \"flex items-center space-x-2 px-3 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors text-sm font-gotham font-medium\",\n                                                                                                children: [\n                                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowDownTrayIcon_ArrowLeftIcon_ArrowRightIcon_BookOpenIcon_ChevronDownIcon_ChevronUpIcon_CloudArrowUpIcon_DocumentIcon_DocumentTextIcon_ExclamationTriangleIcon_EyeIcon_PlusIcon_ShieldCheckIcon_SpeakerWaveIcon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                                                                        className: \"h-4 w-4\"\n                                                                                                    }, void 0, false, {\n                                                                                                        fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-2/page.tsx\",\n                                                                                                        lineNumber: 1320,\n                                                                                                        columnNumber: 43\n                                                                                                    }, this),\n                                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                                        children: \"Upload\"\n                                                                                                    }, void 0, false, {\n                                                                                                        fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-2/page.tsx\",\n                                                                                                        lineNumber: 1321,\n                                                                                                        columnNumber: 43\n                                                                                                    }, this)\n                                                                                                ]\n                                                                                            }, void 0, true, {\n                                                                                                fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-2/page.tsx\",\n                                                                                                lineNumber: 1316,\n                                                                                                columnNumber: 41\n                                                                                            }, this),\n                                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                                                onClick: ()=>handleViewFile(subSection.id),\n                                                                                                className: \"flex items-center space-x-2 px-3 py-2 rounded-lg transition-colors text-sm font-gotham font-medium \".concat(hasFileInSection(subSection.id) ? 'bg-green-600 text-white hover:bg-green-700' : 'bg-gray-400 text-white hover:bg-gray-500'),\n                                                                                                children: [\n                                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowDownTrayIcon_ArrowLeftIcon_ArrowRightIcon_BookOpenIcon_ChevronDownIcon_ChevronUpIcon_CloudArrowUpIcon_DocumentIcon_DocumentTextIcon_ExclamationTriangleIcon_EyeIcon_PlusIcon_ShieldCheckIcon_SpeakerWaveIcon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                                                                        className: \"h-4 w-4\"\n                                                                                                    }, void 0, false, {\n                                                                                                        fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-2/page.tsx\",\n                                                                                                        lineNumber: 1334,\n                                                                                                        columnNumber: 41\n                                                                                                    }, this),\n                                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                                        children: hasFileInSection(subSection.id) ? 'View File' : 'No File'\n                                                                                                    }, void 0, false, {\n                                                                                                        fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-2/page.tsx\",\n                                                                                                        lineNumber: 1335,\n                                                                                                        columnNumber: 41\n                                                                                                    }, this)\n                                                                                                ]\n                                                                                            }, void 0, true, {\n                                                                                                fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-2/page.tsx\",\n                                                                                                lineNumber: 1326,\n                                                                                                columnNumber: 39\n                                                                                            }, this)\n                                                                                        ]\n                                                                                    }, void 0, true),\n                                                                                    canDeleteSubSection(subSection.id) && (user.role === 'admin_super' || user.role === 'admin_biasa') && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                                        onClick: ()=>handleDeleteRequest('subsection', subSection.id, subSection.title),\n                                                                                        className: \"flex items-center space-x-2 px-3 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700 transition-colors text-sm font-gotham font-medium\",\n                                                                                        title: \"Hapus Sub-Section\",\n                                                                                        children: [\n                                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowDownTrayIcon_ArrowLeftIcon_ArrowRightIcon_BookOpenIcon_ChevronDownIcon_ChevronUpIcon_CloudArrowUpIcon_DocumentIcon_DocumentTextIcon_ExclamationTriangleIcon_EyeIcon_PlusIcon_ShieldCheckIcon_SpeakerWaveIcon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                                                                className: \"h-4 w-4\"\n                                                                                            }, void 0, false, {\n                                                                                                fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-2/page.tsx\",\n                                                                                                lineNumber: 1347,\n                                                                                                columnNumber: 39\n                                                                                            }, this),\n                                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                                children: \"Delete\"\n                                                                                            }, void 0, false, {\n                                                                                                fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-2/page.tsx\",\n                                                                                                lineNumber: 1348,\n                                                                                                columnNumber: 39\n                                                                                            }, this)\n                                                                                        ]\n                                                                                    }, void 0, true, {\n                                                                                        fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-2/page.tsx\",\n                                                                                        lineNumber: 1342,\n                                                                                        columnNumber: 37\n                                                                                    }, this),\n                                                                                    subSection.id === 'formulir-risk-assessment' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                                                        children: [\n                                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                                                onClick: ()=>window.open('/forms/risk-assessment', '_blank'),\n                                                                                                className: \"flex items-center space-x-2 px-3 py-2 bg-orange-600 text-white rounded-lg hover:bg-orange-700 transition-colors text-sm font-gotham font-medium\",\n                                                                                                title: \"Buka Formulir Risk Assessment\",\n                                                                                                children: [\n                                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowDownTrayIcon_ArrowLeftIcon_ArrowRightIcon_BookOpenIcon_ChevronDownIcon_ChevronUpIcon_CloudArrowUpIcon_DocumentIcon_DocumentTextIcon_ExclamationTriangleIcon_EyeIcon_PlusIcon_ShieldCheckIcon_SpeakerWaveIcon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                                                                                        className: \"h-4 w-4\"\n                                                                                                    }, void 0, false, {\n                                                                                                        fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-2/page.tsx\",\n                                                                                                        lineNumber: 1360,\n                                                                                                        columnNumber: 41\n                                                                                                    }, this),\n                                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                                        children: \"Isi Formulir\"\n                                                                                                    }, void 0, false, {\n                                                                                                        fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-2/page.tsx\",\n                                                                                                        lineNumber: 1361,\n                                                                                                        columnNumber: 41\n                                                                                                    }, this)\n                                                                                                ]\n                                                                                            }, void 0, true, {\n                                                                                                fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-2/page.tsx\",\n                                                                                                lineNumber: 1355,\n                                                                                                columnNumber: 39\n                                                                                            }, this),\n                                                                                            (user.role === 'admin_super' || user.role === 'admin_biasa') && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                                                onClick: ()=>window.open('/forms/risk-assessment/view', '_blank'),\n                                                                                                className: \"flex items-center space-x-2 px-3 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors text-sm font-gotham font-medium\",\n                                                                                                title: \"Lihat Formulir yang Telah Diisi\",\n                                                                                                children: [\n                                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowDownTrayIcon_ArrowLeftIcon_ArrowRightIcon_BookOpenIcon_ChevronDownIcon_ChevronUpIcon_CloudArrowUpIcon_DocumentIcon_DocumentTextIcon_ExclamationTriangleIcon_EyeIcon_PlusIcon_ShieldCheckIcon_SpeakerWaveIcon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                                                                        className: \"h-4 w-4\"\n                                                                                                    }, void 0, false, {\n                                                                                                        fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-2/page.tsx\",\n                                                                                                        lineNumber: 1371,\n                                                                                                        columnNumber: 43\n                                                                                                    }, this),\n                                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                                        children: \"Lihat Formulir\"\n                                                                                                    }, void 0, false, {\n                                                                                                        fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-2/page.tsx\",\n                                                                                                        lineNumber: 1372,\n                                                                                                        columnNumber: 43\n                                                                                                    }, this)\n                                                                                                ]\n                                                                                            }, void 0, true, {\n                                                                                                fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-2/page.tsx\",\n                                                                                                lineNumber: 1366,\n                                                                                                columnNumber: 41\n                                                                                            }, this)\n                                                                                        ]\n                                                                                    }, void 0, true)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-2/page.tsx\",\n                                                                                lineNumber: 1301,\n                                                                                columnNumber: 33\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-2/page.tsx\",\n                                                                        lineNumber: 1267,\n                                                                        columnNumber: 31\n                                                                    }, this)\n                                                                }, subSection.id, false, {\n                                                                    fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-2/page.tsx\",\n                                                                    lineNumber: 1263,\n                                                                    columnNumber: 29\n                                                                }, this)),\n                                                            (user.role === 'admin_super' || user.role === 'admin_biasa') && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"border-2 border-dashed border-gray-300 rounded-lg p-4 text-center hover:border-blue-400 hover:bg-blue-50 transition-all duration-200\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                        onClick: ()=>handleAddSubSection(subDoc.id),\n                                                                        className: \"flex items-center justify-center space-x-2 mx-auto px-3 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors font-gotham font-medium text-sm\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowDownTrayIcon_ArrowLeftIcon_ArrowRightIcon_BookOpenIcon_ChevronDownIcon_ChevronUpIcon_CloudArrowUpIcon_DocumentIcon_DocumentTextIcon_ExclamationTriangleIcon_EyeIcon_PlusIcon_ShieldCheckIcon_SpeakerWaveIcon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                                                                className: \"h-4 w-4\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-2/page.tsx\",\n                                                                                lineNumber: 1389,\n                                                                                columnNumber: 33\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                children: \"Tambah Sub-Section\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-2/page.tsx\",\n                                                                                lineNumber: 1390,\n                                                                                columnNumber: 33\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-2/page.tsx\",\n                                                                        lineNumber: 1385,\n                                                                        columnNumber: 31\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        className: \"font-gotham text-xs text-gray-500 mt-1\",\n                                                                        children: [\n                                                                            \"Tambahkan sub-section baru untuk \",\n                                                                            subDoc.title\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-2/page.tsx\",\n                                                                        lineNumber: 1392,\n                                                                        columnNumber: 31\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-2/page.tsx\",\n                                                                lineNumber: 1384,\n                                                                columnNumber: 29\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-2/page.tsx\",\n                                                        lineNumber: 1261,\n                                                        columnNumber: 25\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-2/page.tsx\",\n                                                    lineNumber: 1260,\n                                                    columnNumber: 23\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-2/page.tsx\",\n                                                lineNumber: 1259,\n                                                columnNumber: 21\n                                            }, this),\n                                            isExpanded && getAllSubSections(subDoc.id).length === 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"border-t border-gray-200 bg-gray-50 p-6\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"bg-white border border-gray-200 rounded-lg p-4\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center justify-between\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex-1\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                                        className: \"font-gotham-rounded text-base font-semibold text-gray-900 mb-1\",\n                                                                        children: [\n                                                                            String.fromCharCode(65 + index),\n                                                                            \".1 Upload Dokumen \",\n                                                                            subDoc.title\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-2/page.tsx\",\n                                                                        lineNumber: 1408,\n                                                                        columnNumber: 29\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        className: \"font-gotham text-gray-600 text-sm\",\n                                                                        children: [\n                                                                            \"Upload file dokumen untuk \",\n                                                                            subDoc.title\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-2/page.tsx\",\n                                                                        lineNumber: 1411,\n                                                                        columnNumber: 29\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-2/page.tsx\",\n                                                                lineNumber: 1407,\n                                                                columnNumber: 27\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center space-x-2 ml-4\",\n                                                                children: [\n                                                                    (user.role === 'admin_super' || user.role === 'admin_biasa' || user.role === 'scoopers') && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                        onClick: ()=>handleUpload(subDoc.id),\n                                                                        className: \"flex items-center space-x-2 px-3 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors text-sm font-gotham font-medium\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowDownTrayIcon_ArrowLeftIcon_ArrowRightIcon_BookOpenIcon_ChevronDownIcon_ChevronUpIcon_CloudArrowUpIcon_DocumentIcon_DocumentTextIcon_ExclamationTriangleIcon_EyeIcon_PlusIcon_ShieldCheckIcon_SpeakerWaveIcon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                                                className: \"h-4 w-4\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-2/page.tsx\",\n                                                                                lineNumber: 1422,\n                                                                                columnNumber: 33\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                children: \"Upload\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-2/page.tsx\",\n                                                                                lineNumber: 1423,\n                                                                                columnNumber: 33\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-2/page.tsx\",\n                                                                        lineNumber: 1418,\n                                                                        columnNumber: 31\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                        onClick: ()=>handleViewFile(subDoc.id),\n                                                                        className: \"flex items-center space-x-2 px-3 py-2 rounded-lg transition-colors text-sm font-gotham font-medium \".concat(hasFileInSection(subDoc.id) ? 'bg-green-600 text-white hover:bg-green-700' : 'bg-gray-400 text-white hover:bg-gray-500'),\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowDownTrayIcon_ArrowLeftIcon_ArrowRightIcon_BookOpenIcon_ChevronDownIcon_ChevronUpIcon_CloudArrowUpIcon_DocumentIcon_DocumentTextIcon_ExclamationTriangleIcon_EyeIcon_PlusIcon_ShieldCheckIcon_SpeakerWaveIcon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                                                className: \"h-4 w-4\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-2/page.tsx\",\n                                                                                lineNumber: 1436,\n                                                                                columnNumber: 31\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                children: hasFileInSection(subDoc.id) ? 'View File' : 'No File'\n                                                                            }, void 0, false, {\n                                                                                fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-2/page.tsx\",\n                                                                                lineNumber: 1437,\n                                                                                columnNumber: 31\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-2/page.tsx\",\n                                                                        lineNumber: 1428,\n                                                                        columnNumber: 29\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-2/page.tsx\",\n                                                                lineNumber: 1415,\n                                                                columnNumber: 27\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-2/page.tsx\",\n                                                        lineNumber: 1406,\n                                                        columnNumber: 25\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-2/page.tsx\",\n                                                    lineNumber: 1405,\n                                                    columnNumber: 23\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-2/page.tsx\",\n                                                lineNumber: 1404,\n                                                columnNumber: 21\n                                            }, this)\n                                        ]\n                                    }, subDoc.id, true, {\n                                        fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-2/page.tsx\",\n                                        lineNumber: 1207,\n                                        columnNumber: 17\n                                    }, this);\n                                }),\n                                (user.role === 'admin_super' || user.role === 'admin_biasa') && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"border-2 border-dashed border-gray-300 rounded-xl p-6 text-center hover:border-blue-400 hover:bg-blue-50 transition-all duration-200\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: handleAddMainSection,\n                                            className: \"flex items-center justify-center space-x-2 mx-auto px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors font-gotham font-medium\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowDownTrayIcon_ArrowLeftIcon_ArrowRightIcon_BookOpenIcon_ChevronDownIcon_ChevronUpIcon_CloudArrowUpIcon_DocumentIcon_DocumentTextIcon_ExclamationTriangleIcon_EyeIcon_PlusIcon_ShieldCheckIcon_SpeakerWaveIcon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                                    className: \"h-5 w-5\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-2/page.tsx\",\n                                                    lineNumber: 1455,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    children: \"Tambah Section Baru\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-2/page.tsx\",\n                                                    lineNumber: 1456,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-2/page.tsx\",\n                                            lineNumber: 1451,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"font-gotham text-sm text-gray-500 mt-2\",\n                                            children: \"Tambahkan section baru untuk Prosedur dan Instruksi Kerja\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-2/page.tsx\",\n                                            lineNumber: 1458,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-2/page.tsx\",\n                                    lineNumber: 1450,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-2/page.tsx\",\n                            lineNumber: 1201,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"mt-12 bg-blue-50 rounded-xl p-6\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-start space-x-3\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"p-2 bg-blue-100 rounded-lg\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowDownTrayIcon_ArrowLeftIcon_ArrowRightIcon_BookOpenIcon_ChevronDownIcon_ChevronUpIcon_CloudArrowUpIcon_DocumentIcon_DocumentTextIcon_ExclamationTriangleIcon_EyeIcon_PlusIcon_ShieldCheckIcon_SpeakerWaveIcon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                            className: \"h-5 w-5 text-blue-600\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-2/page.tsx\",\n                                            lineNumber: 1469,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-2/page.tsx\",\n                                        lineNumber: 1468,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"font-gotham-rounded text-lg font-semibold text-blue-900 mb-2\",\n                                                children: \"Informasi Dokumen\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-2/page.tsx\",\n                                                lineNumber: 1472,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"font-gotham text-blue-800 text-sm leading-relaxed\",\n                                                children: \"Dokumen Prosedur dan Instruksi Kerja terdiri dari 4 sub-dokumen utama yang mengatur prosedur operasional standar dan instruksi kerja. Setiap sub-dokumen memiliki fokus khusus dalam mendukung implementasi proses bisnis yang efektif dan efisien.\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-2/page.tsx\",\n                                                lineNumber: 1475,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-2/page.tsx\",\n                                        lineNumber: 1471,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-2/page.tsx\",\n                                lineNumber: 1467,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-2/page.tsx\",\n                            lineNumber: 1466,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-2/page.tsx\",\n                    lineNumber: 1164,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-2/page.tsx\",\n                lineNumber: 1163,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(FileManagerModal, {\n                isOpen: showFileManager,\n                onClose: ()=>setShowFileManager(false),\n                sectionId: currentSection,\n                sectionTitle: getSectionTitle(currentSection),\n                userRole: user.role\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-2/page.tsx\",\n                lineNumber: 1487,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(AddSectionModal, {\n                isOpen: showAddSection,\n                onClose: ()=>setShowAddSection(false),\n                onSave: handleSaveNewMainSection,\n                subDocTitle: \"Prosedur dan Instruksi Kerja\"\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-2/page.tsx\",\n                lineNumber: 1496,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(AddSubSectionModal, {\n                isOpen: showAddSubSection,\n                onClose: ()=>setShowAddSubSection(false),\n                onSave: handleSaveNewSubSection,\n                sectionTitle: getSectionTitle(currentSubDocId)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-2/page.tsx\",\n                lineNumber: 1504,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(DeleteConfirmModal, {\n                isOpen: showDeleteConfirm,\n                onClose: ()=>setShowDeleteConfirm(false),\n                onConfirm: handleConfirmDelete,\n                title: deleteTarget.title,\n                type: deleteTarget.type\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-2/page.tsx\",\n                lineNumber: 1512,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-2/page.tsx\",\n        lineNumber: 1162,\n        columnNumber: 5\n    }, this);\n}\n_s3(PedomanPeraturanPage, \"L9/CUVHXNpwTygVSFu0MTNxOmPw=\", false, function() {\n    return [\n        _store_auth__WEBPACK_IMPORTED_MODULE_2__.useAuth,\n        _contexts_ProgramContext__WEBPACK_IMPORTED_MODULE_3__.useProgramDocument\n    ];\n});\n_c5 = PedomanPeraturanPage;\nvar _c, _c1, _c2, _c3, _c4, _c5;\n$RefreshReg$(_c, \"FilePreviewModal\");\n$RefreshReg$(_c1, \"FileManagerModal\");\n$RefreshReg$(_c2, \"AddSectionModal\");\n$RefreshReg$(_c3, \"AddSubSectionModal\");\n$RefreshReg$(_c4, \"DeleteConfirmModal\");\n$RefreshReg$(_c5, \"PedomanPeraturanPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/dokumen/doc-2/page.tsx\n"));

/***/ })

});