"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/dokumen/doc-2/page",{

/***/ "(app-pages-browser)/./src/app/dokumen/doc-2/page.tsx":
/*!****************************************!*\
  !*** ./src/app/dokumen/doc-2/page.tsx ***!
  \****************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ PedomanPeraturanPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _store_auth__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/store/auth */ \"(app-pages-browser)/./src/store/auth.ts\");\n/* harmony import */ var _contexts_ProgramContext__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/contexts/ProgramContext */ \"(app-pages-browser)/./src/contexts/ProgramContext.tsx\");\n/* harmony import */ var _store_progress__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/store/progress */ \"(app-pages-browser)/./src/store/progress.ts\");\n/* harmony import */ var _components_layout_AppLayout__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/layout/AppLayout */ \"(app-pages-browser)/./src/components/layout/AppLayout.tsx\");\n/* harmony import */ var _barrel_optimize_names_ArrowDownTrayIcon_ArrowLeftIcon_ArrowRightIcon_BookOpenIcon_ChevronDownIcon_ChevronUpIcon_CloudArrowUpIcon_DocumentIcon_DocumentTextIcon_ExclamationTriangleIcon_EyeIcon_PlusIcon_ShieldCheckIcon_SpeakerWaveIcon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowDownTrayIcon,ArrowLeftIcon,ArrowRightIcon,BookOpenIcon,ChevronDownIcon,ChevronUpIcon,CloudArrowUpIcon,DocumentIcon,DocumentTextIcon,ExclamationTriangleIcon,EyeIcon,PlusIcon,ShieldCheckIcon,SpeakerWaveIcon,TrashIcon,XMarkIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/DocumentIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowDownTrayIcon_ArrowLeftIcon_ArrowRightIcon_BookOpenIcon_ChevronDownIcon_ChevronUpIcon_CloudArrowUpIcon_DocumentIcon_DocumentTextIcon_ExclamationTriangleIcon_EyeIcon_PlusIcon_ShieldCheckIcon_SpeakerWaveIcon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowDownTrayIcon,ArrowLeftIcon,ArrowRightIcon,BookOpenIcon,ChevronDownIcon,ChevronUpIcon,CloudArrowUpIcon,DocumentIcon,DocumentTextIcon,ExclamationTriangleIcon,EyeIcon,PlusIcon,ShieldCheckIcon,SpeakerWaveIcon,TrashIcon,XMarkIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/XMarkIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowDownTrayIcon_ArrowLeftIcon_ArrowRightIcon_BookOpenIcon_ChevronDownIcon_ChevronUpIcon_CloudArrowUpIcon_DocumentIcon_DocumentTextIcon_ExclamationTriangleIcon_EyeIcon_PlusIcon_ShieldCheckIcon_SpeakerWaveIcon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowDownTrayIcon,ArrowLeftIcon,ArrowRightIcon,BookOpenIcon,ChevronDownIcon,ChevronUpIcon,CloudArrowUpIcon,DocumentIcon,DocumentTextIcon,ExclamationTriangleIcon,EyeIcon,PlusIcon,ShieldCheckIcon,SpeakerWaveIcon,TrashIcon,XMarkIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/ArrowDownTrayIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowDownTrayIcon_ArrowLeftIcon_ArrowRightIcon_BookOpenIcon_ChevronDownIcon_ChevronUpIcon_CloudArrowUpIcon_DocumentIcon_DocumentTextIcon_ExclamationTriangleIcon_EyeIcon_PlusIcon_ShieldCheckIcon_SpeakerWaveIcon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowDownTrayIcon,ArrowLeftIcon,ArrowRightIcon,BookOpenIcon,ChevronDownIcon,ChevronUpIcon,CloudArrowUpIcon,DocumentIcon,DocumentTextIcon,ExclamationTriangleIcon,EyeIcon,PlusIcon,ShieldCheckIcon,SpeakerWaveIcon,TrashIcon,XMarkIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/CloudArrowUpIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowDownTrayIcon_ArrowLeftIcon_ArrowRightIcon_BookOpenIcon_ChevronDownIcon_ChevronUpIcon_CloudArrowUpIcon_DocumentIcon_DocumentTextIcon_ExclamationTriangleIcon_EyeIcon_PlusIcon_ShieldCheckIcon_SpeakerWaveIcon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowDownTrayIcon,ArrowLeftIcon,ArrowRightIcon,BookOpenIcon,ChevronDownIcon,ChevronUpIcon,CloudArrowUpIcon,DocumentIcon,DocumentTextIcon,ExclamationTriangleIcon,EyeIcon,PlusIcon,ShieldCheckIcon,SpeakerWaveIcon,TrashIcon,XMarkIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/EyeIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowDownTrayIcon_ArrowLeftIcon_ArrowRightIcon_BookOpenIcon_ChevronDownIcon_ChevronUpIcon_CloudArrowUpIcon_DocumentIcon_DocumentTextIcon_ExclamationTriangleIcon_EyeIcon_PlusIcon_ShieldCheckIcon_SpeakerWaveIcon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowDownTrayIcon,ArrowLeftIcon,ArrowRightIcon,BookOpenIcon,ChevronDownIcon,ChevronUpIcon,CloudArrowUpIcon,DocumentIcon,DocumentTextIcon,ExclamationTriangleIcon,EyeIcon,PlusIcon,ShieldCheckIcon,SpeakerWaveIcon,TrashIcon,XMarkIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/TrashIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowDownTrayIcon_ArrowLeftIcon_ArrowRightIcon_BookOpenIcon_ChevronDownIcon_ChevronUpIcon_CloudArrowUpIcon_DocumentIcon_DocumentTextIcon_ExclamationTriangleIcon_EyeIcon_PlusIcon_ShieldCheckIcon_SpeakerWaveIcon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowDownTrayIcon,ArrowLeftIcon,ArrowRightIcon,BookOpenIcon,ChevronDownIcon,ChevronUpIcon,CloudArrowUpIcon,DocumentIcon,DocumentTextIcon,ExclamationTriangleIcon,EyeIcon,PlusIcon,ShieldCheckIcon,SpeakerWaveIcon,TrashIcon,XMarkIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/DocumentTextIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowDownTrayIcon_ArrowLeftIcon_ArrowRightIcon_BookOpenIcon_ChevronDownIcon_ChevronUpIcon_CloudArrowUpIcon_DocumentIcon_DocumentTextIcon_ExclamationTriangleIcon_EyeIcon_PlusIcon_ShieldCheckIcon_SpeakerWaveIcon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowDownTrayIcon,ArrowLeftIcon,ArrowRightIcon,BookOpenIcon,ChevronDownIcon,ChevronUpIcon,CloudArrowUpIcon,DocumentIcon,DocumentTextIcon,ExclamationTriangleIcon,EyeIcon,PlusIcon,ShieldCheckIcon,SpeakerWaveIcon,TrashIcon,XMarkIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/ShieldCheckIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowDownTrayIcon_ArrowLeftIcon_ArrowRightIcon_BookOpenIcon_ChevronDownIcon_ChevronUpIcon_CloudArrowUpIcon_DocumentIcon_DocumentTextIcon_ExclamationTriangleIcon_EyeIcon_PlusIcon_ShieldCheckIcon_SpeakerWaveIcon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowDownTrayIcon,ArrowLeftIcon,ArrowRightIcon,BookOpenIcon,ChevronDownIcon,ChevronUpIcon,CloudArrowUpIcon,DocumentIcon,DocumentTextIcon,ExclamationTriangleIcon,EyeIcon,PlusIcon,ShieldCheckIcon,SpeakerWaveIcon,TrashIcon,XMarkIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/BookOpenIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowDownTrayIcon_ArrowLeftIcon_ArrowRightIcon_BookOpenIcon_ChevronDownIcon_ChevronUpIcon_CloudArrowUpIcon_DocumentIcon_DocumentTextIcon_ExclamationTriangleIcon_EyeIcon_PlusIcon_ShieldCheckIcon_SpeakerWaveIcon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowDownTrayIcon,ArrowLeftIcon,ArrowRightIcon,BookOpenIcon,ChevronDownIcon,ChevronUpIcon,CloudArrowUpIcon,DocumentIcon,DocumentTextIcon,ExclamationTriangleIcon,EyeIcon,PlusIcon,ShieldCheckIcon,SpeakerWaveIcon,TrashIcon,XMarkIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/ExclamationTriangleIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowDownTrayIcon_ArrowLeftIcon_ArrowRightIcon_BookOpenIcon_ChevronDownIcon_ChevronUpIcon_CloudArrowUpIcon_DocumentIcon_DocumentTextIcon_ExclamationTriangleIcon_EyeIcon_PlusIcon_ShieldCheckIcon_SpeakerWaveIcon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowDownTrayIcon,ArrowLeftIcon,ArrowRightIcon,BookOpenIcon,ChevronDownIcon,ChevronUpIcon,CloudArrowUpIcon,DocumentIcon,DocumentTextIcon,ExclamationTriangleIcon,EyeIcon,PlusIcon,ShieldCheckIcon,SpeakerWaveIcon,TrashIcon,XMarkIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/SpeakerWaveIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowDownTrayIcon_ArrowLeftIcon_ArrowRightIcon_BookOpenIcon_ChevronDownIcon_ChevronUpIcon_CloudArrowUpIcon_DocumentIcon_DocumentTextIcon_ExclamationTriangleIcon_EyeIcon_PlusIcon_ShieldCheckIcon_SpeakerWaveIcon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowDownTrayIcon,ArrowLeftIcon,ArrowRightIcon,BookOpenIcon,ChevronDownIcon,ChevronUpIcon,CloudArrowUpIcon,DocumentIcon,DocumentTextIcon,ExclamationTriangleIcon,EyeIcon,PlusIcon,ShieldCheckIcon,SpeakerWaveIcon,TrashIcon,XMarkIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/ArrowLeftIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowDownTrayIcon_ArrowLeftIcon_ArrowRightIcon_BookOpenIcon_ChevronDownIcon_ChevronUpIcon_CloudArrowUpIcon_DocumentIcon_DocumentTextIcon_ExclamationTriangleIcon_EyeIcon_PlusIcon_ShieldCheckIcon_SpeakerWaveIcon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowDownTrayIcon,ArrowLeftIcon,ArrowRightIcon,BookOpenIcon,ChevronDownIcon,ChevronUpIcon,CloudArrowUpIcon,DocumentIcon,DocumentTextIcon,ExclamationTriangleIcon,EyeIcon,PlusIcon,ShieldCheckIcon,SpeakerWaveIcon,TrashIcon,XMarkIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/ChevronUpIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowDownTrayIcon_ArrowLeftIcon_ArrowRightIcon_BookOpenIcon_ChevronDownIcon_ChevronUpIcon_CloudArrowUpIcon_DocumentIcon_DocumentTextIcon_ExclamationTriangleIcon_EyeIcon_PlusIcon_ShieldCheckIcon_SpeakerWaveIcon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowDownTrayIcon,ArrowLeftIcon,ArrowRightIcon,BookOpenIcon,ChevronDownIcon,ChevronUpIcon,CloudArrowUpIcon,DocumentIcon,DocumentTextIcon,ExclamationTriangleIcon,EyeIcon,PlusIcon,ShieldCheckIcon,SpeakerWaveIcon,TrashIcon,XMarkIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/ChevronDownIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowDownTrayIcon_ArrowLeftIcon_ArrowRightIcon_BookOpenIcon_ChevronDownIcon_ChevronUpIcon_CloudArrowUpIcon_DocumentIcon_DocumentTextIcon_ExclamationTriangleIcon_EyeIcon_PlusIcon_ShieldCheckIcon_SpeakerWaveIcon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowDownTrayIcon,ArrowLeftIcon,ArrowRightIcon,BookOpenIcon,ChevronDownIcon,ChevronUpIcon,CloudArrowUpIcon,DocumentIcon,DocumentTextIcon,ExclamationTriangleIcon,EyeIcon,PlusIcon,ShieldCheckIcon,SpeakerWaveIcon,TrashIcon,XMarkIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/ArrowRightIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowDownTrayIcon_ArrowLeftIcon_ArrowRightIcon_BookOpenIcon_ChevronDownIcon_ChevronUpIcon_CloudArrowUpIcon_DocumentIcon_DocumentTextIcon_ExclamationTriangleIcon_EyeIcon_PlusIcon_ShieldCheckIcon_SpeakerWaveIcon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowDownTrayIcon,ArrowLeftIcon,ArrowRightIcon,BookOpenIcon,ChevronDownIcon,ChevronUpIcon,CloudArrowUpIcon,DocumentIcon,DocumentTextIcon,ExclamationTriangleIcon,EyeIcon,PlusIcon,ShieldCheckIcon,SpeakerWaveIcon,TrashIcon,XMarkIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/PlusIcon.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$(), _s1 = $RefreshSig$(), _s2 = $RefreshSig$(), _s3 = $RefreshSig$();\n\n\n\n\n\n\n// Sub-documents for Prosedur dan Instruksi Kerja\nconst SUB_DOCUMENTS = [];\n// Mock files data\nconst MOCK_FILES = [\n    {\n        id: '1',\n        name: 'SOP_Procurement_Process_v1.2.pdf',\n        type: 'pdf',\n        size: '2.1 MB',\n        uploadDate: '2024-01-15',\n        uploadedBy: 'Admin Super'\n    },\n    {\n        id: '2',\n        name: 'Work_Instruction_Vendor_Evaluation.docx',\n        type: 'docx',\n        size: '1.5 MB',\n        uploadDate: '2024-01-14',\n        uploadedBy: 'Admin Biasa'\n    }\n];\nfunction FilePreviewModal(param) {\n    let { isOpen, onClose, fileName, fileType, fileContent, mimeType } = param;\n    if (!isOpen) return null;\n    const renderPreview = ()=>{\n        const extension = fileType.toLowerCase();\n        // If no content available, show placeholder\n        if (!fileContent) {\n            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"w-full h-[600px] bg-gray-100 rounded-lg flex items-center justify-center\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-center\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowDownTrayIcon_ArrowLeftIcon_ArrowRightIcon_BookOpenIcon_ChevronDownIcon_ChevronUpIcon_CloudArrowUpIcon_DocumentIcon_DocumentTextIcon_ExclamationTriangleIcon_EyeIcon_PlusIcon_ShieldCheckIcon_SpeakerWaveIcon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                            className: \"h-16 w-16 text-gray-600 mx-auto mb-4\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-2/page.tsx\",\n                            lineNumber: 73,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"font-gotham text-gray-700 mb-2\",\n                            children: \"No Preview Available\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-2/page.tsx\",\n                            lineNumber: 74,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"font-gotham text-sm text-gray-500\",\n                            children: fileName\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-2/page.tsx\",\n                            lineNumber: 75,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"font-gotham text-xs text-gray-400 mt-2\",\n                            children: \"File content not available for preview.\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-2/page.tsx\",\n                            lineNumber: 76,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-2/page.tsx\",\n                    lineNumber: 72,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-2/page.tsx\",\n                lineNumber: 71,\n                columnNumber: 9\n            }, this);\n        }\n        if (extension === 'pdf') {\n            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"w-full h-[600px] bg-gray-100 rounded-lg overflow-hidden\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"iframe\", {\n                    src: fileContent,\n                    className: \"w-full h-full border-0\",\n                    title: \"PDF Preview: \".concat(fileName)\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-2/page.tsx\",\n                    lineNumber: 87,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-2/page.tsx\",\n                lineNumber: 86,\n                columnNumber: 9\n            }, this);\n        }\n        if ([\n            'jpg',\n            'jpeg',\n            'png',\n            'gif',\n            'webp'\n        ].includes(extension)) {\n            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"w-full h-[600px] bg-gray-100 rounded-lg overflow-hidden flex items-center justify-center\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                    src: fileContent,\n                    alt: fileName,\n                    className: \"max-w-full max-h-full object-contain\"\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-2/page.tsx\",\n                    lineNumber: 99,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-2/page.tsx\",\n                lineNumber: 98,\n                columnNumber: 9\n            }, this);\n        }\n        if ([\n            'doc',\n            'docx'\n        ].includes(extension)) {\n            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"w-full h-[600px] bg-gray-100 rounded-lg flex items-center justify-center\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-center\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowDownTrayIcon_ArrowLeftIcon_ArrowRightIcon_BookOpenIcon_ChevronDownIcon_ChevronUpIcon_CloudArrowUpIcon_DocumentIcon_DocumentTextIcon_ExclamationTriangleIcon_EyeIcon_PlusIcon_ShieldCheckIcon_SpeakerWaveIcon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                            className: \"h-16 w-16 text-blue-600 mx-auto mb-4\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-2/page.tsx\",\n                            lineNumber: 112,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"font-gotham text-gray-700 mb-2\",\n                            children: \"Word Document\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-2/page.tsx\",\n                            lineNumber: 113,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"font-gotham text-sm text-gray-500\",\n                            children: fileName\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-2/page.tsx\",\n                            lineNumber: 114,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"font-gotham text-xs text-gray-400 mt-2\",\n                            children: \"Preview dokumen Word tidak tersedia. Silakan download untuk melihat file.\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-2/page.tsx\",\n                            lineNumber: 115,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-2/page.tsx\",\n                    lineNumber: 111,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-2/page.tsx\",\n                lineNumber: 110,\n                columnNumber: 9\n            }, this);\n        }\n        if ([\n            'xls',\n            'xlsx'\n        ].includes(extension)) {\n            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"w-full h-[600px] bg-gray-100 rounded-lg flex items-center justify-center\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-center\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowDownTrayIcon_ArrowLeftIcon_ArrowRightIcon_BookOpenIcon_ChevronDownIcon_ChevronUpIcon_CloudArrowUpIcon_DocumentIcon_DocumentTextIcon_ExclamationTriangleIcon_EyeIcon_PlusIcon_ShieldCheckIcon_SpeakerWaveIcon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                            className: \"h-16 w-16 text-green-600 mx-auto mb-4\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-2/page.tsx\",\n                            lineNumber: 127,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"font-gotham text-gray-700 mb-2\",\n                            children: \"Excel Spreadsheet\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-2/page.tsx\",\n                            lineNumber: 128,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"font-gotham text-sm text-gray-500\",\n                            children: fileName\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-2/page.tsx\",\n                            lineNumber: 129,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"font-gotham text-xs text-gray-400 mt-2\",\n                            children: \"Preview spreadsheet tidak tersedia. Silakan download untuk melihat file.\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-2/page.tsx\",\n                            lineNumber: 130,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-2/page.tsx\",\n                    lineNumber: 126,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-2/page.tsx\",\n                lineNumber: 125,\n                columnNumber: 9\n            }, this);\n        }\n        if ([\n            'ppt',\n            'pptx'\n        ].includes(extension)) {\n            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"w-full h-[600px] bg-gray-100 rounded-lg flex items-center justify-center\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-center\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowDownTrayIcon_ArrowLeftIcon_ArrowRightIcon_BookOpenIcon_ChevronDownIcon_ChevronUpIcon_CloudArrowUpIcon_DocumentIcon_DocumentTextIcon_ExclamationTriangleIcon_EyeIcon_PlusIcon_ShieldCheckIcon_SpeakerWaveIcon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                            className: \"h-16 w-16 text-orange-600 mx-auto mb-4\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-2/page.tsx\",\n                            lineNumber: 142,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"font-gotham text-gray-700 mb-2\",\n                            children: \"PowerPoint Presentation\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-2/page.tsx\",\n                            lineNumber: 143,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"font-gotham text-sm text-gray-500\",\n                            children: fileName\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-2/page.tsx\",\n                            lineNumber: 144,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"font-gotham text-xs text-gray-400 mt-2\",\n                            children: \"Preview presentasi tidak tersedia. Silakan download untuk melihat file.\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-2/page.tsx\",\n                            lineNumber: 145,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-2/page.tsx\",\n                    lineNumber: 141,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-2/page.tsx\",\n                lineNumber: 140,\n                columnNumber: 9\n            }, this);\n        }\n        // Default preview for unknown file types\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"w-full h-[600px] bg-gray-100 rounded-lg flex items-center justify-center\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowDownTrayIcon_ArrowLeftIcon_ArrowRightIcon_BookOpenIcon_ChevronDownIcon_ChevronUpIcon_CloudArrowUpIcon_DocumentIcon_DocumentTextIcon_ExclamationTriangleIcon_EyeIcon_PlusIcon_ShieldCheckIcon_SpeakerWaveIcon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                        className: \"h-16 w-16 text-gray-600 mx-auto mb-4\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-2/page.tsx\",\n                        lineNumber: 157,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"font-gotham text-gray-700 mb-2\",\n                        children: \"File Preview\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-2/page.tsx\",\n                        lineNumber: 158,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"font-gotham text-sm text-gray-500\",\n                        children: fileName\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-2/page.tsx\",\n                        lineNumber: 159,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"font-gotham text-xs text-gray-400 mt-2\",\n                        children: \"Preview tidak tersedia untuk tipe file ini. Silakan download untuk melihat file.\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-2/page.tsx\",\n                        lineNumber: 160,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-2/page.tsx\",\n                lineNumber: 156,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-2/page.tsx\",\n            lineNumber: 155,\n            columnNumber: 7\n        }, this);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-[60]\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"bg-white rounded-xl shadow-xl max-w-6xl w-full mx-4 max-h-[95vh] overflow-hidden\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"p-6 border-b border-gray-200\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center justify-between\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                className: \"font-gotham-rounded text-xl font-bold text-gray-900\",\n                                children: \"File Preview\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-2/page.tsx\",\n                                lineNumber: 173,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: onClose,\n                                className: \"p-2 hover:bg-gray-100 rounded-lg transition-colors\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowDownTrayIcon_ArrowLeftIcon_ArrowRightIcon_BookOpenIcon_ChevronDownIcon_ChevronUpIcon_CloudArrowUpIcon_DocumentIcon_DocumentTextIcon_ExclamationTriangleIcon_EyeIcon_PlusIcon_ShieldCheckIcon_SpeakerWaveIcon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                    className: \"h-6 w-6 text-gray-500\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-2/page.tsx\",\n                                    lineNumber: 180,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-2/page.tsx\",\n                                lineNumber: 176,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-2/page.tsx\",\n                        lineNumber: 172,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-2/page.tsx\",\n                    lineNumber: 171,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"p-6\",\n                    children: [\n                        renderPreview(),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"mt-6 flex justify-center space-x-3\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: ()=>{\n                                        if (fileContent) {\n                                            const link = document.createElement('a');\n                                            link.href = fileContent;\n                                            link.download = fileName;\n                                            document.body.appendChild(link);\n                                            link.click();\n                                            document.body.removeChild(link);\n                                        } else {\n                                            alert('File content not available for download');\n                                        }\n                                    },\n                                    className: \"flex items-center space-x-2 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors font-gotham font-medium\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowDownTrayIcon_ArrowLeftIcon_ArrowRightIcon_BookOpenIcon_ChevronDownIcon_ChevronUpIcon_CloudArrowUpIcon_DocumentIcon_DocumentTextIcon_ExclamationTriangleIcon_EyeIcon_PlusIcon_ShieldCheckIcon_SpeakerWaveIcon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                            className: \"h-4 w-4\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-2/page.tsx\",\n                                            lineNumber: 204,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            children: \"Download\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-2/page.tsx\",\n                                            lineNumber: 205,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-2/page.tsx\",\n                                    lineNumber: 189,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: onClose,\n                                    className: \"flex items-center space-x-2 px-4 py-2 bg-gray-600 text-white rounded-lg hover:bg-gray-700 transition-colors font-gotham font-medium\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowDownTrayIcon_ArrowLeftIcon_ArrowRightIcon_BookOpenIcon_ChevronDownIcon_ChevronUpIcon_CloudArrowUpIcon_DocumentIcon_DocumentTextIcon_ExclamationTriangleIcon_EyeIcon_PlusIcon_ShieldCheckIcon_SpeakerWaveIcon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                            className: \"h-4 w-4\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-2/page.tsx\",\n                                            lineNumber: 211,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            children: \"Close\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-2/page.tsx\",\n                                            lineNumber: 212,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-2/page.tsx\",\n                                    lineNumber: 207,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-2/page.tsx\",\n                            lineNumber: 188,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-2/page.tsx\",\n                    lineNumber: 185,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-2/page.tsx\",\n            lineNumber: 170,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-2/page.tsx\",\n        lineNumber: 169,\n        columnNumber: 5\n    }, this);\n}\n_c = FilePreviewModal;\nfunction FileManagerModal(param) {\n    let { isOpen, onClose, sectionId, sectionTitle, userRole } = param;\n    _s();\n    // Store files per section in localStorage\n    const [sectionFiles, setSectionFiles] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({});\n    // Load files from localStorage after component mounts\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"FileManagerModal.useEffect\": ()=>{\n            const loadSectionFiles = {\n                \"FileManagerModal.useEffect.loadSectionFiles\": ()=>{\n                    try {\n                        const saved = localStorage.getItem('doc-1-section-files');\n                        if (saved) {\n                            setSectionFiles(JSON.parse(saved));\n                        }\n                    } catch (error) {\n                        console.error('Error loading section files from localStorage:', error);\n                    }\n                }\n            }[\"FileManagerModal.useEffect.loadSectionFiles\"];\n            loadSectionFiles();\n        }\n    }[\"FileManagerModal.useEffect\"], []);\n    const [selectedFile, setSelectedFile] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [isUploading, setIsUploading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [showPreview, setShowPreview] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [previewFile, setPreviewFile] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        name: '',\n        type: '',\n        content: '',\n        mimeType: ''\n    });\n    // Close preview when file manager is closed\n    const handleClose = ()=>{\n        setShowPreview(false);\n        onClose();\n    };\n    // Get files for current section\n    const currentSectionFiles = sectionFiles[sectionId] || [];\n    const hasFile = currentSectionFiles.length > 0;\n    // Check if user can upload (not karyawan or reviewer)\n    const canUpload = userRole !== 'karyawan' && userRole !== 'reviewer';\n    // Save to localStorage whenever sectionFiles changes\n    const updateSectionFiles = (newSectionFiles)=>{\n        setSectionFiles(newSectionFiles);\n        try {\n            localStorage.setItem('doc-1-section-files', JSON.stringify(newSectionFiles));\n        } catch (error) {\n            console.error('Error saving section files to localStorage:', error);\n        }\n    };\n    if (!isOpen) return null;\n    const handleFileSelect = (event)=>{\n        var _event_target_files;\n        const file = (_event_target_files = event.target.files) === null || _event_target_files === void 0 ? void 0 : _event_target_files[0];\n        if (file) {\n            setSelectedFile(file);\n        }\n    };\n    const handleUpload = async ()=>{\n        if (!selectedFile || hasFile) return; // Prevent upload if already has file\n        setIsUploading(true);\n        // Convert file to base64 for storage\n        const fileReader = new FileReader();\n        fileReader.onload = ()=>{\n            const fileContent = fileReader.result;\n            const newFile = {\n                id: Date.now().toString(),\n                name: selectedFile.name,\n                type: selectedFile.name.split('.').pop() || 'unknown',\n                size: \"\".concat((selectedFile.size / 1024 / 1024).toFixed(1), \" MB\"),\n                uploadDate: new Date().toISOString().split('T')[0],\n                uploadedBy: 'Current User',\n                content: fileContent,\n                mimeType: selectedFile.type\n            };\n            // Update files for this section only (max 1 file)\n            const newSectionFiles = {\n                ...sectionFiles,\n                [sectionId]: [\n                    newFile\n                ] // Only one file per section\n            };\n            updateSectionFiles(newSectionFiles);\n            setSelectedFile(null);\n            setIsUploading(false);\n            alert('File berhasil diupload!');\n        };\n        fileReader.onerror = ()=>{\n            setIsUploading(false);\n            alert('Error reading file!');\n        };\n        // Read file as data URL (base64)\n        fileReader.readAsDataURL(selectedFile);\n    };\n    const handleDelete = (fileId)=>{\n        if (confirm('Apakah Anda yakin ingin menghapus file ini?')) {\n            const newSectionFiles = {\n                ...sectionFiles,\n                [sectionId]: currentSectionFiles.filter((file)=>file.id !== fileId)\n            };\n            updateSectionFiles(newSectionFiles);\n        }\n    };\n    const handleView = (file)=>{\n        setPreviewFile({\n            name: file.name,\n            type: file.type,\n            content: file.content,\n            mimeType: file.mimeType\n        });\n        setShowPreview(true);\n    };\n    const handleDownload = (fileName)=>{\n        alert(\"Mengunduh file: \".concat(fileName));\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-white rounded-xl shadow-xl max-w-4xl w-full max-h-[90vh] overflow-hidden\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center justify-between p-6 border-b border-gray-200\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                        className: \"font-gotham-rounded text-xl font-bold text-gray-900\",\n                                        children: \"File Manager\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-2/page.tsx\",\n                                        lineNumber: 365,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"font-gotham text-gray-600 text-sm mt-1\",\n                                        children: sectionTitle\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-2/page.tsx\",\n                                        lineNumber: 368,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-2/page.tsx\",\n                                lineNumber: 364,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: handleClose,\n                                className: \"p-2 hover:bg-gray-100 rounded-lg transition-colors\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowDownTrayIcon_ArrowLeftIcon_ArrowRightIcon_BookOpenIcon_ChevronDownIcon_ChevronUpIcon_CloudArrowUpIcon_DocumentIcon_DocumentTextIcon_ExclamationTriangleIcon_EyeIcon_PlusIcon_ShieldCheckIcon_SpeakerWaveIcon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                    className: \"h-6 w-6 text-gray-500\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-2/page.tsx\",\n                                    lineNumber: 376,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-2/page.tsx\",\n                                lineNumber: 372,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-2/page.tsx\",\n                        lineNumber: 363,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"p-6 max-h-[calc(90vh-120px)] overflow-y-auto\",\n                        children: [\n                            canUpload && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-gray-50 rounded-lg p-4 mb-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"font-gotham-rounded font-semibold text-gray-900 mb-3\",\n                                        children: \"Upload File Baru\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-2/page.tsx\",\n                                        lineNumber: 384,\n                                        columnNumber: 15\n                                    }, this),\n                                    hasFile ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-yellow-50 border border-yellow-200 rounded-lg p-3\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"font-gotham text-yellow-800 text-sm\",\n                                            children: \"⚠️ Sub-dokumen ini sudah memiliki file. Hapus file yang ada terlebih dahulu untuk upload file baru.\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-2/page.tsx\",\n                                            lineNumber: 390,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-2/page.tsx\",\n                                        lineNumber: 389,\n                                        columnNumber: 17\n                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-3\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                type: \"file\",\n                                                onChange: handleFileSelect,\n                                                className: \"block w-full text-sm text-gray-500 file:mr-4 file:py-2 file:px-4 file:rounded-lg file:border-0 file:text-sm file:font-medium file:bg-blue-50 file:text-blue-700 hover:file:bg-blue-100\",\n                                                accept: \".pdf,.doc,.docx,.xls,.xlsx,.ppt,.pptx\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-2/page.tsx\",\n                                                lineNumber: 396,\n                                                columnNumber: 19\n                                            }, this),\n                                            selectedFile && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"bg-white rounded-lg p-3 border\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center justify-between\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center space-x-3\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowDownTrayIcon_ArrowLeftIcon_ArrowRightIcon_BookOpenIcon_ChevronDownIcon_ChevronUpIcon_CloudArrowUpIcon_DocumentIcon_DocumentTextIcon_ExclamationTriangleIcon_EyeIcon_PlusIcon_ShieldCheckIcon_SpeakerWaveIcon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                                                    className: \"h-6 w-6 text-blue-600\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-2/page.tsx\",\n                                                                    lineNumber: 407,\n                                                                    columnNumber: 27\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                            className: \"font-gotham font-medium text-gray-900 text-sm\",\n                                                                            children: selectedFile.name\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-2/page.tsx\",\n                                                                            lineNumber: 409,\n                                                                            columnNumber: 29\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                            className: \"font-gotham text-xs text-gray-500\",\n                                                                            children: [\n                                                                                (selectedFile.size / 1024 / 1024).toFixed(1),\n                                                                                \" MB\"\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-2/page.tsx\",\n                                                                            lineNumber: 410,\n                                                                            columnNumber: 29\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-2/page.tsx\",\n                                                                    lineNumber: 408,\n                                                                    columnNumber: 27\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-2/page.tsx\",\n                                                            lineNumber: 406,\n                                                            columnNumber: 25\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                            onClick: handleUpload,\n                                                            disabled: isUploading,\n                                                            className: \"flex items-center space-x-2 px-3 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors font-gotham font-medium text-sm\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowDownTrayIcon_ArrowLeftIcon_ArrowRightIcon_BookOpenIcon_ChevronDownIcon_ChevronUpIcon_CloudArrowUpIcon_DocumentIcon_DocumentTextIcon_ExclamationTriangleIcon_EyeIcon_PlusIcon_ShieldCheckIcon_SpeakerWaveIcon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                                    className: \"h-4 w-4\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-2/page.tsx\",\n                                                                    lineNumber: 420,\n                                                                    columnNumber: 27\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    children: isUploading ? 'Uploading...' : 'Upload'\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-2/page.tsx\",\n                                                                    lineNumber: 421,\n                                                                    columnNumber: 27\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-2/page.tsx\",\n                                                            lineNumber: 415,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-2/page.tsx\",\n                                                    lineNumber: 405,\n                                                    columnNumber: 23\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-2/page.tsx\",\n                                                lineNumber: 404,\n                                                columnNumber: 21\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-2/page.tsx\",\n                                        lineNumber: 395,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-2/page.tsx\",\n                                lineNumber: 383,\n                                columnNumber: 13\n                            }, this),\n                            !canUpload && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-blue-50 border border-blue-200 rounded-lg p-4 mb-6\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center space-x-3\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowDownTrayIcon_ArrowLeftIcon_ArrowRightIcon_BookOpenIcon_ChevronDownIcon_ChevronUpIcon_CloudArrowUpIcon_DocumentIcon_DocumentTextIcon_ExclamationTriangleIcon_EyeIcon_PlusIcon_ShieldCheckIcon_SpeakerWaveIcon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                            className: \"h-5 w-5 text-blue-600\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-2/page.tsx\",\n                                            lineNumber: 435,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                    className: \"font-gotham-rounded font-semibold text-blue-900 text-sm\",\n                                                    children: \"Mode View Only\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-2/page.tsx\",\n                                                    lineNumber: 437,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"font-gotham text-blue-800 text-xs\",\n                                                    children: \"Anda dapat melihat dan mendownload file, namun tidak dapat mengupload file baru.\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-2/page.tsx\",\n                                                    lineNumber: 440,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-2/page.tsx\",\n                                            lineNumber: 436,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-2/page.tsx\",\n                                    lineNumber: 434,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-2/page.tsx\",\n                                lineNumber: 433,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"font-gotham-rounded font-semibold text-gray-900 mb-3\",\n                                        children: \"File yang Tersedia\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-2/page.tsx\",\n                                        lineNumber: 450,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-2\",\n                                        children: [\n                                            currentSectionFiles.map((file)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"bg-white border border-gray-200 rounded-lg p-4 hover:bg-gray-50 transition-colors\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center justify-between\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center space-x-3\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowDownTrayIcon_ArrowLeftIcon_ArrowRightIcon_BookOpenIcon_ChevronDownIcon_ChevronUpIcon_CloudArrowUpIcon_DocumentIcon_DocumentTextIcon_ExclamationTriangleIcon_EyeIcon_PlusIcon_ShieldCheckIcon_SpeakerWaveIcon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                                                        className: \"h-6 w-6 text-blue-600\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-2/page.tsx\",\n                                                                        lineNumber: 459,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                                                className: \"font-gotham font-medium text-gray-900 text-sm\",\n                                                                                children: file.name\n                                                                            }, void 0, false, {\n                                                                                fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-2/page.tsx\",\n                                                                                lineNumber: 461,\n                                                                                columnNumber: 25\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"flex items-center space-x-3 text-xs text-gray-500 font-gotham\",\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                        children: file.size\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-2/page.tsx\",\n                                                                                        lineNumber: 463,\n                                                                                        columnNumber: 27\n                                                                                    }, this),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                        children: \"•\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-2/page.tsx\",\n                                                                                        lineNumber: 464,\n                                                                                        columnNumber: 27\n                                                                                    }, this),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                        children: file.uploadDate\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-2/page.tsx\",\n                                                                                        lineNumber: 465,\n                                                                                        columnNumber: 27\n                                                                                    }, this),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                        children: \"•\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-2/page.tsx\",\n                                                                                        lineNumber: 466,\n                                                                                        columnNumber: 27\n                                                                                    }, this),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                        children: file.uploadedBy\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-2/page.tsx\",\n                                                                                        lineNumber: 467,\n                                                                                        columnNumber: 27\n                                                                                    }, this)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-2/page.tsx\",\n                                                                                lineNumber: 462,\n                                                                                columnNumber: 25\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-2/page.tsx\",\n                                                                        lineNumber: 460,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-2/page.tsx\",\n                                                                lineNumber: 458,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center space-x-1\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                        onClick: ()=>handleView(file),\n                                                                        className: \"p-2 text-gray-600 hover:text-blue-600 hover:bg-blue-50 rounded-lg transition-colors\",\n                                                                        title: \"View File\",\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowDownTrayIcon_ArrowLeftIcon_ArrowRightIcon_BookOpenIcon_ChevronDownIcon_ChevronUpIcon_CloudArrowUpIcon_DocumentIcon_DocumentTextIcon_ExclamationTriangleIcon_EyeIcon_PlusIcon_ShieldCheckIcon_SpeakerWaveIcon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                                            className: \"h-4 w-4\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-2/page.tsx\",\n                                                                            lineNumber: 478,\n                                                                            columnNumber: 25\n                                                                        }, this)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-2/page.tsx\",\n                                                                        lineNumber: 473,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                        onClick: ()=>handleDownload(file.name),\n                                                                        className: \"p-2 text-gray-600 hover:text-green-600 hover:bg-green-50 rounded-lg transition-colors\",\n                                                                        title: \"Download File\",\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowDownTrayIcon_ArrowLeftIcon_ArrowRightIcon_BookOpenIcon_ChevronDownIcon_ChevronUpIcon_CloudArrowUpIcon_DocumentIcon_DocumentTextIcon_ExclamationTriangleIcon_EyeIcon_PlusIcon_ShieldCheckIcon_SpeakerWaveIcon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                                            className: \"h-4 w-4\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-2/page.tsx\",\n                                                                            lineNumber: 485,\n                                                                            columnNumber: 25\n                                                                        }, this)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-2/page.tsx\",\n                                                                        lineNumber: 480,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    canUpload && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                        onClick: ()=>handleDelete(file.id),\n                                                                        className: \"p-2 text-gray-600 hover:text-red-600 hover:bg-red-50 rounded-lg transition-colors\",\n                                                                        title: \"Delete File\",\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowDownTrayIcon_ArrowLeftIcon_ArrowRightIcon_BookOpenIcon_ChevronDownIcon_ChevronUpIcon_CloudArrowUpIcon_DocumentIcon_DocumentTextIcon_ExclamationTriangleIcon_EyeIcon_PlusIcon_ShieldCheckIcon_SpeakerWaveIcon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                                            className: \"h-4 w-4\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-2/page.tsx\",\n                                                                            lineNumber: 495,\n                                                                            columnNumber: 27\n                                                                        }, this)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-2/page.tsx\",\n                                                                        lineNumber: 490,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-2/page.tsx\",\n                                                                lineNumber: 472,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-2/page.tsx\",\n                                                        lineNumber: 457,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                }, file.id, false, {\n                                                    fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-2/page.tsx\",\n                                                    lineNumber: 456,\n                                                    columnNumber: 17\n                                                }, this)),\n                                            currentSectionFiles.length === 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-center py-8\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowDownTrayIcon_ArrowLeftIcon_ArrowRightIcon_BookOpenIcon_ChevronDownIcon_ChevronUpIcon_CloudArrowUpIcon_DocumentIcon_DocumentTextIcon_ExclamationTriangleIcon_EyeIcon_PlusIcon_ShieldCheckIcon_SpeakerWaveIcon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                                        className: \"h-12 w-12 text-gray-400 mx-auto mb-3\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-2/page.tsx\",\n                                                        lineNumber: 505,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"font-gotham text-gray-500 text-sm\",\n                                                        children: \"Belum ada file yang diupload untuk sub-dokumen ini\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-2/page.tsx\",\n                                                        lineNumber: 506,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"font-gotham text-gray-400 text-xs mt-1\",\n                                                        children: \"Maksimal 1 file per sub-dokumen\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-2/page.tsx\",\n                                                        lineNumber: 509,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-2/page.tsx\",\n                                                lineNumber: 504,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-2/page.tsx\",\n                                        lineNumber: 454,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-2/page.tsx\",\n                                lineNumber: 449,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-2/page.tsx\",\n                        lineNumber: 380,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-2/page.tsx\",\n                lineNumber: 361,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(FilePreviewModal, {\n                isOpen: showPreview,\n                onClose: ()=>setShowPreview(false),\n                fileName: previewFile.name,\n                fileType: previewFile.type,\n                fileContent: previewFile.content,\n                mimeType: previewFile.mimeType\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-2/page.tsx\",\n                lineNumber: 520,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-2/page.tsx\",\n        lineNumber: 360,\n        columnNumber: 5\n    }, this);\n}\n_s(FileManagerModal, \"WwKpo+R8DCEmGqCXleOmG/2pUVs=\");\n_c1 = FileManagerModal;\nfunction AddSectionModal(param) {\n    let { isOpen, onClose, onSave, subDocTitle } = param;\n    _s1();\n    const [title, setTitle] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [description, setDescription] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    if (!isOpen) return null;\n    const handleSubmit = (e)=>{\n        e.preventDefault();\n        if (title.trim()) {\n            onSave(title.trim(), description.trim());\n            setTitle('');\n            setDescription('');\n        }\n    };\n    const handleClose = ()=>{\n        setTitle('');\n        setDescription('');\n        onClose();\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"bg-white rounded-xl p-6 w-full max-w-md mx-4\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center justify-between mb-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                            className: \"font-gotham-rounded text-lg font-semibold text-gray-900\",\n                            children: \"Tambah Section Baru\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-2/page.tsx\",\n                            lineNumber: 565,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: handleClose,\n                            className: \"p-2 hover:bg-gray-100 rounded-lg transition-colors\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowDownTrayIcon_ArrowLeftIcon_ArrowRightIcon_BookOpenIcon_ChevronDownIcon_ChevronUpIcon_CloudArrowUpIcon_DocumentIcon_DocumentTextIcon_ExclamationTriangleIcon_EyeIcon_PlusIcon_ShieldCheckIcon_SpeakerWaveIcon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                className: \"h-5 w-5 text-gray-500\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-2/page.tsx\",\n                                lineNumber: 572,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-2/page.tsx\",\n                            lineNumber: 568,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-2/page.tsx\",\n                    lineNumber: 564,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                    className: \"font-gotham text-sm text-gray-600 mb-4\",\n                    children: [\n                        \"Menambah section baru untuk: \",\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            className: \"font-medium\",\n                            children: subDocTitle\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-2/page.tsx\",\n                            lineNumber: 577,\n                            columnNumber: 40\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-2/page.tsx\",\n                    lineNumber: 576,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                    onSubmit: handleSubmit,\n                    className: \"space-y-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                    className: \"block font-gotham text-sm font-medium text-gray-700 mb-2\",\n                                    children: \"Judul Section\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-2/page.tsx\",\n                                    lineNumber: 582,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                    type: \"text\",\n                                    value: title,\n                                    onChange: (e)=>setTitle(e.target.value),\n                                    className: \"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 font-gotham\",\n                                    placeholder: \"Masukkan judul section...\",\n                                    required: true\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-2/page.tsx\",\n                                    lineNumber: 585,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-2/page.tsx\",\n                            lineNumber: 581,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                    className: \"block font-gotham text-sm font-medium text-gray-700 mb-2\",\n                                    children: [\n                                        \"Deskripsi \",\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-gray-400 font-normal\",\n                                            children: \"(opsional)\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-2/page.tsx\",\n                                            lineNumber: 597,\n                                            columnNumber: 25\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-2/page.tsx\",\n                                    lineNumber: 596,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                                    value: description,\n                                    onChange: (e)=>setDescription(e.target.value),\n                                    rows: 3,\n                                    className: \"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 font-gotham\",\n                                    placeholder: \"Masukkan deskripsi section (opsional)...\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-2/page.tsx\",\n                                    lineNumber: 599,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-2/page.tsx\",\n                            lineNumber: 595,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex space-x-3 pt-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    type: \"button\",\n                                    onClick: handleClose,\n                                    className: \"flex-1 px-4 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors font-gotham font-medium\",\n                                    children: \"Batal\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-2/page.tsx\",\n                                    lineNumber: 609,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    type: \"submit\",\n                                    disabled: !title.trim(),\n                                    className: \"flex-1 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:bg-gray-300 disabled:cursor-not-allowed transition-colors font-gotham font-medium\",\n                                    children: \"Tambah Section\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-2/page.tsx\",\n                                    lineNumber: 616,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-2/page.tsx\",\n                            lineNumber: 608,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-2/page.tsx\",\n                    lineNumber: 580,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-2/page.tsx\",\n            lineNumber: 563,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-2/page.tsx\",\n        lineNumber: 562,\n        columnNumber: 5\n    }, this);\n}\n_s1(AddSectionModal, \"1UKQWTfo2RWmkwPNsekAdQbvqFk=\");\n_c2 = AddSectionModal;\nfunction AddSubSectionModal(param) {\n    let { isOpen, onClose, onSave, sectionTitle, sectionId } = param;\n    _s2();\n    const [title, setTitle] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [description, setDescription] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [type, setType] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('upload');\n    const [formUrl, setFormUrl] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [enableMapping, setEnableMapping] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [selectedTargetSection, setSelectedTargetSection] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const { getDocumentProgress } = (0,_store_progress__WEBPACK_IMPORTED_MODULE_4__.useProgressStore)();\n    // Get available sections from Evidence Mapping Klausul (doc-3)\n    const doc3Progress = getDocumentProgress('doc-3');\n    const availableTargetSections = (doc3Progress === null || doc3Progress === void 0 ? void 0 : doc3Progress.sections) || [];\n    if (!isOpen) return null;\n    const handleSubmit = (e)=>{\n        e.preventDefault();\n        if (title.trim() && (type === 'upload' || type === 'form' && formUrl.trim())) {\n            onSave(title.trim(), description.trim(), type, type === 'form' ? formUrl.trim() : undefined);\n            setTitle('');\n            setDescription('');\n            setType('upload');\n            setFormUrl('');\n        }\n    };\n    const handleClose = ()=>{\n        setTitle('');\n        setDescription('');\n        setType('upload');\n        setFormUrl('');\n        onClose();\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"bg-white rounded-xl p-6 w-full max-w-md mx-4\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center justify-between mb-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                            className: \"font-gotham-rounded text-lg font-semibold text-gray-900\",\n                            children: \"Tambah Sub-Section Baru\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-2/page.tsx\",\n                            lineNumber: 681,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: handleClose,\n                            className: \"p-2 hover:bg-gray-100 rounded-lg transition-colors\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowDownTrayIcon_ArrowLeftIcon_ArrowRightIcon_BookOpenIcon_ChevronDownIcon_ChevronUpIcon_CloudArrowUpIcon_DocumentIcon_DocumentTextIcon_ExclamationTriangleIcon_EyeIcon_PlusIcon_ShieldCheckIcon_SpeakerWaveIcon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                className: \"h-5 w-5 text-gray-500\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-2/page.tsx\",\n                                lineNumber: 688,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-2/page.tsx\",\n                            lineNumber: 684,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-2/page.tsx\",\n                    lineNumber: 680,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                    className: \"font-gotham text-sm text-gray-600 mb-4\",\n                    children: [\n                        \"Menambah sub-section baru untuk: \",\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            className: \"font-medium\",\n                            children: sectionTitle\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-2/page.tsx\",\n                            lineNumber: 693,\n                            columnNumber: 44\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-2/page.tsx\",\n                    lineNumber: 692,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                    onSubmit: handleSubmit,\n                    className: \"space-y-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                    className: \"block font-gotham text-sm font-medium text-gray-700 mb-2\",\n                                    children: \"Judul Sub-Section\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-2/page.tsx\",\n                                    lineNumber: 698,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                    type: \"text\",\n                                    value: title,\n                                    onChange: (e)=>setTitle(e.target.value),\n                                    className: \"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 font-gotham\",\n                                    placeholder: \"Masukkan judul sub-section...\",\n                                    required: true\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-2/page.tsx\",\n                                    lineNumber: 701,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-2/page.tsx\",\n                            lineNumber: 697,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                    className: \"block font-gotham text-sm font-medium text-gray-700 mb-2\",\n                                    children: [\n                                        \"Deskripsi \",\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-gray-400 font-normal\",\n                                            children: \"(opsional)\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-2/page.tsx\",\n                                            lineNumber: 713,\n                                            columnNumber: 25\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-2/page.tsx\",\n                                    lineNumber: 712,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                                    value: description,\n                                    onChange: (e)=>setDescription(e.target.value),\n                                    rows: 3,\n                                    className: \"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 font-gotham\",\n                                    placeholder: \"Masukkan deskripsi sub-section (opsional)...\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-2/page.tsx\",\n                                    lineNumber: 715,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-2/page.tsx\",\n                            lineNumber: 711,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                    className: \"block font-gotham text-sm font-medium text-gray-700 mb-2\",\n                                    children: \"Tipe Sub-Section\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-2/page.tsx\",\n                                    lineNumber: 725,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"space-y-3\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            className: \"flex items-center p-3 border border-gray-200 rounded-lg hover:bg-gray-50 cursor-pointer\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                    type: \"radio\",\n                                                    name: \"subsectionType\",\n                                                    value: \"upload\",\n                                                    checked: type === 'upload',\n                                                    onChange: (e)=>setType(e.target.value),\n                                                    className: \"mr-3\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-2/page.tsx\",\n                                                    lineNumber: 730,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center space-x-3\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"p-2 bg-blue-100 text-blue-600 rounded-lg\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowDownTrayIcon_ArrowLeftIcon_ArrowRightIcon_BookOpenIcon_ChevronDownIcon_ChevronUpIcon_CloudArrowUpIcon_DocumentIcon_DocumentTextIcon_ExclamationTriangleIcon_EyeIcon_PlusIcon_ShieldCheckIcon_SpeakerWaveIcon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                                className: \"h-5 w-5\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-2/page.tsx\",\n                                                                lineNumber: 740,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-2/page.tsx\",\n                                                            lineNumber: 739,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"font-gotham font-medium text-gray-900\",\n                                                                    children: \"Upload Dokumen\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-2/page.tsx\",\n                                                                    lineNumber: 743,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"font-gotham text-sm text-gray-600\",\n                                                                    children: \"Sub-section untuk upload file dokumen\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-2/page.tsx\",\n                                                                    lineNumber: 744,\n                                                                    columnNumber: 21\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-2/page.tsx\",\n                                                            lineNumber: 742,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-2/page.tsx\",\n                                                    lineNumber: 738,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-2/page.tsx\",\n                                            lineNumber: 729,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            className: \"flex items-center p-3 border border-gray-200 rounded-lg hover:bg-gray-50 cursor-pointer\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                    type: \"radio\",\n                                                    name: \"subsectionType\",\n                                                    value: \"form\",\n                                                    checked: type === 'form',\n                                                    onChange: (e)=>setType(e.target.value),\n                                                    className: \"mr-3\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-2/page.tsx\",\n                                                    lineNumber: 750,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center space-x-3\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"p-2 bg-green-100 text-green-600 rounded-lg\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowDownTrayIcon_ArrowLeftIcon_ArrowRightIcon_BookOpenIcon_ChevronDownIcon_ChevronUpIcon_CloudArrowUpIcon_DocumentIcon_DocumentTextIcon_ExclamationTriangleIcon_EyeIcon_PlusIcon_ShieldCheckIcon_SpeakerWaveIcon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                                                className: \"h-5 w-5\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-2/page.tsx\",\n                                                                lineNumber: 760,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-2/page.tsx\",\n                                                            lineNumber: 759,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"font-gotham font-medium text-gray-900\",\n                                                                    children: \"Isi Formulir\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-2/page.tsx\",\n                                                                    lineNumber: 763,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"font-gotham text-sm text-gray-600\",\n                                                                    children: \"Sub-section untuk mengisi formulir online\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-2/page.tsx\",\n                                                                    lineNumber: 764,\n                                                                    columnNumber: 21\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-2/page.tsx\",\n                                                            lineNumber: 762,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-2/page.tsx\",\n                                                    lineNumber: 758,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-2/page.tsx\",\n                                            lineNumber: 749,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-2/page.tsx\",\n                                    lineNumber: 728,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-2/page.tsx\",\n                            lineNumber: 724,\n                            columnNumber: 11\n                        }, this),\n                        type === 'form' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                    className: \"block font-gotham text-sm font-medium text-gray-700 mb-2\",\n                                    children: \"URL Formulir\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-2/page.tsx\",\n                                    lineNumber: 773,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                    type: \"url\",\n                                    value: formUrl,\n                                    onChange: (e)=>setFormUrl(e.target.value),\n                                    className: \"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 font-gotham\",\n                                    placeholder: \"Contoh: /forms/risk-assessment\",\n                                    required: true\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-2/page.tsx\",\n                                    lineNumber: 776,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"font-gotham text-xs text-gray-500 mt-1\",\n                                    children: \"Masukkan URL relatif atau absolut untuk formulir\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-2/page.tsx\",\n                                    lineNumber: 784,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-2/page.tsx\",\n                            lineNumber: 772,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex space-x-3 pt-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    type: \"button\",\n                                    onClick: handleClose,\n                                    className: \"flex-1 px-4 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors font-gotham font-medium\",\n                                    children: \"Batal\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-2/page.tsx\",\n                                    lineNumber: 791,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    type: \"submit\",\n                                    disabled: !title.trim() || type === 'form' && !formUrl.trim(),\n                                    className: \"flex-1 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:bg-gray-300 disabled:cursor-not-allowed transition-colors font-gotham font-medium\",\n                                    children: \"Tambah Sub-Section\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-2/page.tsx\",\n                                    lineNumber: 798,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-2/page.tsx\",\n                            lineNumber: 790,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-2/page.tsx\",\n                    lineNumber: 696,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-2/page.tsx\",\n            lineNumber: 679,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-2/page.tsx\",\n        lineNumber: 678,\n        columnNumber: 5\n    }, this);\n}\n_s2(AddSubSectionModal, \"l/dJwNkulLyhENXaSZPVTaZSclU=\", false, function() {\n    return [\n        _store_progress__WEBPACK_IMPORTED_MODULE_4__.useProgressStore\n    ];\n});\n_c3 = AddSubSectionModal;\nfunction DeleteConfirmModal(param) {\n    let { isOpen, onClose, onConfirm, title, type } = param;\n    if (!isOpen) return null;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"bg-white rounded-xl p-6 w-full max-w-md mx-4\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center justify-between mb-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                            className: \"font-gotham-rounded text-lg font-semibold text-red-900\",\n                            children: [\n                                \"Hapus \",\n                                type === 'section' ? 'Section' : 'Sub-Section'\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-2/page.tsx\",\n                            lineNumber: 828,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: onClose,\n                            className: \"p-2 hover:bg-gray-100 rounded-lg transition-colors\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowDownTrayIcon_ArrowLeftIcon_ArrowRightIcon_BookOpenIcon_ChevronDownIcon_ChevronUpIcon_CloudArrowUpIcon_DocumentIcon_DocumentTextIcon_ExclamationTriangleIcon_EyeIcon_PlusIcon_ShieldCheckIcon_SpeakerWaveIcon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                className: \"h-5 w-5 text-gray-500\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-2/page.tsx\",\n                                lineNumber: 835,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-2/page.tsx\",\n                            lineNumber: 831,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-2/page.tsx\",\n                    lineNumber: 827,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"mb-6\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center space-x-3 mb-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"p-3 bg-red-100 rounded-lg\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowDownTrayIcon_ArrowLeftIcon_ArrowRightIcon_BookOpenIcon_ChevronDownIcon_ChevronUpIcon_CloudArrowUpIcon_DocumentIcon_DocumentTextIcon_ExclamationTriangleIcon_EyeIcon_PlusIcon_ShieldCheckIcon_SpeakerWaveIcon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                        className: \"h-6 w-6 text-red-600\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-2/page.tsx\",\n                                        lineNumber: 842,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-2/page.tsx\",\n                                    lineNumber: 841,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"font-gotham font-medium text-gray-900\",\n                                            children: [\n                                                \"Apakah Anda yakin ingin menghapus \",\n                                                type === 'section' ? 'section' : 'sub-section',\n                                                \" ini?\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-2/page.tsx\",\n                                            lineNumber: 845,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"font-gotham text-sm text-gray-600 mt-1\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"font-medium\",\n                                                children: [\n                                                    '\"',\n                                                    title,\n                                                    '\"'\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-2/page.tsx\",\n                                                lineNumber: 849,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-2/page.tsx\",\n                                            lineNumber: 848,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-2/page.tsx\",\n                                    lineNumber: 844,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-2/page.tsx\",\n                            lineNumber: 840,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-red-50 border border-red-200 rounded-lg p-3\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"font-gotham text-sm text-red-800\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"font-medium\",\n                                        children: \"Peringatan:\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-2/page.tsx\",\n                                        lineNumber: 856,\n                                        columnNumber: 15\n                                    }, this),\n                                    \" Tindakan ini tidak dapat dibatalkan.\",\n                                    type === 'section' ? ' Semua sub-section dan file yang terkait akan ikut terhapus.' : ' File yang terkait dengan sub-section ini akan ikut terhapus.'\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-2/page.tsx\",\n                                lineNumber: 855,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-2/page.tsx\",\n                            lineNumber: 854,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-2/page.tsx\",\n                    lineNumber: 839,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex space-x-3\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: onClose,\n                            className: \"flex-1 px-4 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors font-gotham font-medium\",\n                            children: \"Batal\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-2/page.tsx\",\n                            lineNumber: 866,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: onConfirm,\n                            className: \"flex-1 px-4 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700 transition-colors font-gotham font-medium\",\n                            children: \"Hapus\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-2/page.tsx\",\n                            lineNumber: 872,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-2/page.tsx\",\n                    lineNumber: 865,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-2/page.tsx\",\n            lineNumber: 826,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-2/page.tsx\",\n        lineNumber: 825,\n        columnNumber: 5\n    }, this);\n}\n_c4 = DeleteConfirmModal;\nfunction PedomanPeraturanPage() {\n    _s3();\n    const { user } = (0,_store_auth__WEBPACK_IMPORTED_MODULE_2__.useAuth)();\n    const { dynamicSections: dynamicMainSections, dynamicSubSections, updateDynamicSections: updateDynamicMainSections, updateDynamicSubSections } = (0,_contexts_ProgramContext__WEBPACK_IMPORTED_MODULE_3__.useProgramDocument)('doc2');\n    const [expandedSection, setExpandedSection] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [showFileManager, setShowFileManager] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [currentSection, setCurrentSection] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [showAddSection, setShowAddSection] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [showAddSubSection, setShowAddSubSection] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [showDeleteConfirm, setShowDeleteConfirm] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [currentSubDocId, setCurrentSubDocId] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [deleteTarget, setDeleteTarget] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        type: 'section',\n        id: '',\n        title: ''\n    });\n    if (!user) return null;\n    const handleSectionToggle = (sectionId)=>{\n        setExpandedSection(expandedSection === sectionId ? null : sectionId);\n    };\n    const handleSubSectionToggle = (subSectionId)=>{\n        setExpandedSection(expandedSection === subSectionId ? null : subSectionId);\n    };\n    const handleUpload = (subSectionId)=>{\n        setCurrentSection(subSectionId);\n        setShowFileManager(true);\n    };\n    const handleViewFile = (subSectionId)=>{\n        // Get files from localStorage for this section\n        const savedFiles = localStorage.getItem('doc-1-section-files');\n        const sectionFiles = savedFiles ? JSON.parse(savedFiles) : {};\n        const filesForSection = sectionFiles[subSectionId] || [];\n        if (filesForSection.length === 0) {\n            alert('Belum ada file yang diupload untuk sub-dokumen ini.');\n            return;\n        }\n        // If there's a file, show it in the file manager\n        setCurrentSection(subSectionId);\n        setShowFileManager(true);\n    };\n    // Handle adding new main section (E, F, G, etc.)\n    const handleAddMainSection = ()=>{\n        setShowAddSection(true);\n    };\n    // Handle adding new sub-section (A.4, B.1, etc.)\n    const handleAddSubSection = (subDocId)=>{\n        setCurrentSubDocId(subDocId);\n        setShowAddSubSection(true);\n    };\n    // Save new main section\n    const handleSaveNewMainSection = (title, description)=>{\n        const newSectionId = \"dynamic-section-\".concat(Date.now());\n        const newSection = {\n            id: newSectionId,\n            title: title,\n            description: description,\n            iconType: 'DocumentTextIcon',\n            color: 'bg-purple-100 text-purple-600',\n            hoverColor: 'hover:border-purple-500 hover:bg-purple-50',\n            iconHoverColor: 'hover:text-purple-600',\n            subSections: [],\n            createdAt: new Date().toISOString(),\n            createdBy: user.name\n        };\n        const updatedSections = [\n            ...dynamicMainSections,\n            newSection\n        ];\n        updateDynamicMainSections(updatedSections);\n        setShowAddSection(false);\n    };\n    // Save new sub-section\n    const handleSaveNewSubSection = (title, description, type, formUrl)=>{\n        const subDocSubSections = dynamicSubSections[currentSubDocId] || [];\n        const newSubSectionId = \"\".concat(currentSubDocId, \"-subsection-\").concat(Date.now());\n        const newSubSection = {\n            id: newSubSectionId,\n            title: title,\n            description: description,\n            type: type,\n            formUrl: type === 'form' ? formUrl : undefined,\n            createdAt: new Date().toISOString(),\n            createdBy: user.name\n        };\n        const updatedSubSections = {\n            ...dynamicSubSections,\n            [currentSubDocId]: [\n                ...subDocSubSections,\n                newSubSection\n            ]\n        };\n        updateDynamicSubSections(updatedSubSections);\n        setShowAddSubSection(false);\n        setCurrentSubDocId('');\n    };\n    // Handle delete section/sub-section\n    const handleDeleteRequest = (type, id, title)=>{\n        setDeleteTarget({\n            type,\n            id,\n            title\n        });\n        setShowDeleteConfirm(true);\n    };\n    const handleConfirmDelete = ()=>{\n        if (deleteTarget.type === 'section') {\n            // Delete main section\n            const updatedSections = dynamicMainSections.filter((section)=>section.id !== deleteTarget.id);\n            updateDynamicMainSections(updatedSections);\n            // Also delete all sub-sections for this section\n            const updatedSubSections = {\n                ...dynamicSubSections\n            };\n            delete updatedSubSections[deleteTarget.id];\n            updateDynamicSubSections(updatedSubSections);\n            // Delete all files for this section and its sub-sections\n            cleanupSectionFiles(deleteTarget.id);\n        } else if (deleteTarget.type === 'subsection') {\n            // Delete sub-section\n            const sectionId = findSectionIdForSubSection(deleteTarget.id);\n            if (sectionId) {\n                const updatedSubSections = {\n                    ...dynamicSubSections,\n                    [sectionId]: dynamicSubSections[sectionId].filter((subSection)=>subSection.id !== deleteTarget.id)\n                };\n                updateDynamicSubSections(updatedSubSections);\n                // Delete files for this sub-section\n                cleanupSubSectionFiles(deleteTarget.id);\n            }\n        }\n        setShowDeleteConfirm(false);\n        setDeleteTarget({\n            type: 'section',\n            id: '',\n            title: ''\n        });\n    };\n    // Helper function to find section ID for a sub-section\n    const findSectionIdForSubSection = (subSectionId)=>{\n        for(const sectionId in dynamicSubSections){\n            if (dynamicSubSections[sectionId].some((subSection)=>subSection.id === subSectionId)) {\n                return sectionId;\n            }\n        }\n        return null;\n    };\n    // Cleanup files when deleting section/sub-section\n    const cleanupSectionFiles = (sectionId)=>{\n        try {\n            const savedFiles = localStorage.getItem('doc-1-section-files');\n            if (savedFiles) {\n                const sectionFiles = JSON.parse(savedFiles);\n                // Remove files for the section itself\n                delete sectionFiles[sectionId];\n                // Remove files for all sub-sections of this section\n                const subSections = dynamicSubSections[sectionId] || [];\n                subSections.forEach((subSection)=>{\n                    delete sectionFiles[subSection.id];\n                });\n                localStorage.setItem('doc-1-section-files', JSON.stringify(sectionFiles));\n            }\n        } catch (error) {\n            console.error('Error cleaning up section files:', error);\n        }\n    };\n    const cleanupSubSectionFiles = (subSectionId)=>{\n        try {\n            const savedFiles = localStorage.getItem('doc-1-section-files');\n            if (savedFiles) {\n                const sectionFiles = JSON.parse(savedFiles);\n                delete sectionFiles[subSectionId];\n                localStorage.setItem('doc-1-section-files', JSON.stringify(sectionFiles));\n            }\n        } catch (error) {\n            console.error('Error cleaning up sub-section files:', error);\n        }\n    };\n    // Get icon component from string identifier\n    const getIconComponent = (iconType)=>{\n        const iconMap = {\n            'DocumentTextIcon': _barrel_optimize_names_ArrowDownTrayIcon_ArrowLeftIcon_ArrowRightIcon_BookOpenIcon_ChevronDownIcon_ChevronUpIcon_CloudArrowUpIcon_DocumentIcon_DocumentTextIcon_ExclamationTriangleIcon_EyeIcon_PlusIcon_ShieldCheckIcon_SpeakerWaveIcon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_12__[\"default\"],\n            'ShieldCheckIcon': _barrel_optimize_names_ArrowDownTrayIcon_ArrowLeftIcon_ArrowRightIcon_BookOpenIcon_ChevronDownIcon_ChevronUpIcon_CloudArrowUpIcon_DocumentIcon_DocumentTextIcon_ExclamationTriangleIcon_EyeIcon_PlusIcon_ShieldCheckIcon_SpeakerWaveIcon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_13__[\"default\"],\n            'BookOpenIcon': _barrel_optimize_names_ArrowDownTrayIcon_ArrowLeftIcon_ArrowRightIcon_BookOpenIcon_ChevronDownIcon_ChevronUpIcon_CloudArrowUpIcon_DocumentIcon_DocumentTextIcon_ExclamationTriangleIcon_EyeIcon_PlusIcon_ShieldCheckIcon_SpeakerWaveIcon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_14__[\"default\"],\n            'ExclamationTriangleIcon': _barrel_optimize_names_ArrowDownTrayIcon_ArrowLeftIcon_ArrowRightIcon_BookOpenIcon_ChevronDownIcon_ChevronUpIcon_CloudArrowUpIcon_DocumentIcon_DocumentTextIcon_ExclamationTriangleIcon_EyeIcon_PlusIcon_ShieldCheckIcon_SpeakerWaveIcon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_15__[\"default\"],\n            'SpeakerWaveIcon': _barrel_optimize_names_ArrowDownTrayIcon_ArrowLeftIcon_ArrowRightIcon_BookOpenIcon_ChevronDownIcon_ChevronUpIcon_CloudArrowUpIcon_DocumentIcon_DocumentTextIcon_ExclamationTriangleIcon_EyeIcon_PlusIcon_ShieldCheckIcon_SpeakerWaveIcon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_16__[\"default\"]\n        };\n        return iconMap[iconType] || _barrel_optimize_names_ArrowDownTrayIcon_ArrowLeftIcon_ArrowRightIcon_BookOpenIcon_ChevronDownIcon_ChevronUpIcon_CloudArrowUpIcon_DocumentIcon_DocumentTextIcon_ExclamationTriangleIcon_EyeIcon_PlusIcon_ShieldCheckIcon_SpeakerWaveIcon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_12__[\"default\"];\n    };\n    // Get all sections (static + dynamic)\n    const getAllSections = ()=>{\n        // Add icon component and ensure all required properties for dynamic sections\n        const dynamicSectionsWithIcons = dynamicMainSections.map((section)=>({\n                ...section,\n                icon: getIconComponent(section.iconType || 'DocumentTextIcon'),\n                subSections: section.subSections || [],\n                color: section.color || 'bg-purple-100 text-purple-600',\n                hoverColor: section.hoverColor || 'hover:border-purple-500 hover:bg-purple-50',\n                iconHoverColor: section.iconHoverColor || 'hover:text-purple-600'\n            }));\n        return [\n            ...SUB_DOCUMENTS,\n            ...dynamicSectionsWithIcons\n        ];\n    };\n    // Get all sub-sections for a section (static + dynamic)\n    const getAllSubSections = (sectionId)=>{\n        const staticSection = SUB_DOCUMENTS.find((doc)=>doc.id === sectionId);\n        const staticSubSections = staticSection ? staticSection.subSections : [];\n        const dynamicSubSectionsForSection = dynamicSubSections[sectionId] || [];\n        return [\n            ...staticSubSections,\n            ...dynamicSubSectionsForSection\n        ];\n    };\n    // Check if section can be deleted (only dynamic sections)\n    const canDeleteSection = (sectionId)=>{\n        return dynamicMainSections.some((section)=>section.id === sectionId);\n    };\n    // Check if sub-section can be deleted (only dynamic sub-sections)\n    const canDeleteSubSection = (subSectionId)=>{\n        for(const sectionId in dynamicSubSections){\n            if (dynamicSubSections[sectionId].some((subSection)=>subSection.id === subSectionId)) {\n                return true;\n            }\n        }\n        return false;\n    };\n    const handleBack = ()=>{\n        window.history.back();\n    };\n    const getSectionTitle = (sectionId)=>{\n        const sectionTitles = {\n            'manual-smap': 'Manual SMAP',\n            'struktur-organisasi': 'Struktur Organisasi SMAP Telkom',\n            'kebijakan-anti-suap': 'Kebijakan Anti Suap Telkom',\n            'formulir-risk-assessment': 'Formulir Penilaian Risiko Penyuapan (Risk Assessment)',\n            'sub-doc-2': 'Code of Conduct',\n            'sub-doc-3': 'Conflict of Interest',\n            'sub-doc-4': 'Whistle Blower System'\n        };\n        // Check if it's a dynamic main section\n        const dynamicMainSection = dynamicMainSections.find((section)=>section.id === sectionId);\n        if (dynamicMainSection) {\n            return dynamicMainSection.title;\n        }\n        // Check if it's a dynamic sub-section\n        for(const subDocId in dynamicSubSections){\n            const subSections = dynamicSubSections[subDocId];\n            const dynamicSubSection = subSections.find((subSection)=>subSection.id === sectionId);\n            if (dynamicSubSection) {\n                return dynamicSubSection.title;\n            }\n        }\n        return sectionTitles[sectionId] || sectionId;\n    };\n    const hasFileInSection = (sectionId)=>{\n        const savedFiles = localStorage.getItem('doc-1-section-files');\n        const sectionFiles = savedFiles ? JSON.parse(savedFiles) : {};\n        const filesForSection = sectionFiles[sectionId] || [];\n        return filesForSection.length > 0;\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_layout_AppLayout__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"container mx-auto px-4 py-8\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"max-w-6xl mx-auto\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"mb-8\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center space-x-3 mb-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"p-3 bg-red-100 rounded-xl\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowDownTrayIcon_ArrowLeftIcon_ArrowRightIcon_BookOpenIcon_ChevronDownIcon_ChevronUpIcon_CloudArrowUpIcon_DocumentIcon_DocumentTextIcon_ExclamationTriangleIcon_EyeIcon_PlusIcon_ShieldCheckIcon_SpeakerWaveIcon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                className: \"h-8 w-8 text-red-600\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-2/page.tsx\",\n                                                lineNumber: 1181,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-2/page.tsx\",\n                                            lineNumber: 1180,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                                    className: \"font-gotham-rounded text-3xl font-bold text-gray-900\",\n                                                    children: \"Prosedur dan Instruksi Kerja\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-2/page.tsx\",\n                                                    lineNumber: 1184,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"font-gotham text-gray-600 mt-1\",\n                                                    children: \"Prosedur operasional standar dan instruksi kerja\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-2/page.tsx\",\n                                                    lineNumber: 1187,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-2/page.tsx\",\n                                            lineNumber: 1183,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-2/page.tsx\",\n                                    lineNumber: 1179,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                                    className: \"flex items-center space-x-2 text-sm text-gray-500 font-gotham mb-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                            href: \"/dashboard\",\n                                            className: \"hover:text-red-600 transition-colors\",\n                                            children: \"Dashboard\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-2/page.tsx\",\n                                            lineNumber: 1195,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            children: \"›\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-2/page.tsx\",\n                                            lineNumber: 1198,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-gray-900\",\n                                            children: \"Prosedur dan Instruksi Kerja\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-2/page.tsx\",\n                                            lineNumber: 1199,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-2/page.tsx\",\n                                    lineNumber: 1194,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: handleBack,\n                                    className: \"flex items-center space-x-2 px-4 py-2 bg-gray-100 text-gray-700 rounded-lg hover:bg-gray-200 transition-colors font-gotham font-medium\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowDownTrayIcon_ArrowLeftIcon_ArrowRightIcon_BookOpenIcon_ChevronDownIcon_ChevronUpIcon_CloudArrowUpIcon_DocumentIcon_DocumentTextIcon_ExclamationTriangleIcon_EyeIcon_PlusIcon_ShieldCheckIcon_SpeakerWaveIcon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                            className: \"h-4 w-4\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-2/page.tsx\",\n                                            lineNumber: 1207,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            children: \"Kembali\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-2/page.tsx\",\n                                            lineNumber: 1208,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-2/page.tsx\",\n                                    lineNumber: 1203,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-2/page.tsx\",\n                            lineNumber: 1178,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-6\",\n                            children: [\n                                getAllSections().map((subDoc, index)=>{\n                                    const IconComponent = subDoc.icon;\n                                    const isExpanded = expandedSection === subDoc.id;\n                                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"border border-gray-200 rounded-xl\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"p-6 cursor-pointer hover:bg-gray-50 transition-all duration-200 \".concat(subDoc.hoverColor),\n                                                onClick: ()=>handleSectionToggle(subDoc.id),\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-start justify-between\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-start space-x-4\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"p-3 rounded-xl \".concat(subDoc.color),\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(IconComponent, {\n                                                                        className: \"h-6 w-6\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-2/page.tsx\",\n                                                                        lineNumber: 1231,\n                                                                        columnNumber: 27\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-2/page.tsx\",\n                                                                    lineNumber: 1230,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex-1\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                            className: \"font-gotham-rounded text-lg font-semibold text-gray-900 mb-2\",\n                                                                            children: [\n                                                                                String.fromCharCode(65 + index),\n                                                                                \". \",\n                                                                                subDoc.title\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-2/page.tsx\",\n                                                                            lineNumber: 1234,\n                                                                            columnNumber: 27\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                            className: \"font-gotham text-gray-600 text-sm leading-relaxed\",\n                                                                            children: subDoc.description\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-2/page.tsx\",\n                                                                            lineNumber: 1237,\n                                                                            columnNumber: 27\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-2/page.tsx\",\n                                                                    lineNumber: 1233,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-2/page.tsx\",\n                                                            lineNumber: 1229,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center space-x-2\",\n                                                            children: [\n                                                                canDeleteSection(subDoc.id) && (user.role === 'admin_super' || user.role === 'admin_biasa') && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                    onClick: (e)=>{\n                                                                        e.stopPropagation(); // Prevent section toggle\n                                                                        handleDeleteRequest('section', subDoc.id, subDoc.title);\n                                                                    },\n                                                                    className: \"p-2 border border-red-300 rounded-lg hover:bg-red-50 hover:border-red-400 transition-all duration-200 group\",\n                                                                    title: \"Hapus Section\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowDownTrayIcon_ArrowLeftIcon_ArrowRightIcon_BookOpenIcon_ChevronDownIcon_ChevronUpIcon_CloudArrowUpIcon_DocumentIcon_DocumentTextIcon_ExclamationTriangleIcon_EyeIcon_PlusIcon_ShieldCheckIcon_SpeakerWaveIcon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                                        className: \"h-4 w-4 text-red-500 group-hover:text-red-600\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-2/page.tsx\",\n                                                                        lineNumber: 1253,\n                                                                        columnNumber: 29\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-2/page.tsx\",\n                                                                    lineNumber: 1245,\n                                                                    columnNumber: 27\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"p-2 border border-gray-300 rounded-lg transition-all duration-200 \".concat(subDoc.hoverColor),\n                                                                    children: isExpanded ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowDownTrayIcon_ArrowLeftIcon_ArrowRightIcon_BookOpenIcon_ChevronDownIcon_ChevronUpIcon_CloudArrowUpIcon_DocumentIcon_DocumentTextIcon_ExclamationTriangleIcon_EyeIcon_PlusIcon_ShieldCheckIcon_SpeakerWaveIcon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                                        className: \"h-5 w-5 text-gray-600 transition-colors duration-200 \".concat(subDoc.iconHoverColor)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-2/page.tsx\",\n                                                                        lineNumber: 1260,\n                                                                        columnNumber: 29\n                                                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowDownTrayIcon_ArrowLeftIcon_ArrowRightIcon_BookOpenIcon_ChevronDownIcon_ChevronUpIcon_CloudArrowUpIcon_DocumentIcon_DocumentTextIcon_ExclamationTriangleIcon_EyeIcon_PlusIcon_ShieldCheckIcon_SpeakerWaveIcon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                                                        className: \"h-5 w-5 text-gray-600 transition-colors duration-200 \".concat(subDoc.iconHoverColor)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-2/page.tsx\",\n                                                                        lineNumber: 1262,\n                                                                        columnNumber: 29\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-2/page.tsx\",\n                                                                    lineNumber: 1258,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-2/page.tsx\",\n                                                            lineNumber: 1242,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-2/page.tsx\",\n                                                    lineNumber: 1228,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-2/page.tsx\",\n                                                lineNumber: 1224,\n                                                columnNumber: 19\n                                            }, this),\n                                            isExpanded && (getAllSubSections(subDoc.id).length > 0 || user.role === 'admin_super' || user.role === 'admin_biasa') && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"border-t border-gray-200 bg-gray-50\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"p-6\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"space-y-4\",\n                                                        children: [\n                                                            getAllSubSections(subDoc.id).map((subSection, subIndex)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"bg-white border border-gray-200 rounded-lg p-4\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"flex items-center justify-between\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"flex-1\",\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                        className: \"flex items-center space-x-2 mb-2\",\n                                                                                        children: [\n                                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                                className: \"p-1 rounded \".concat(subSection.type === 'form' ? 'bg-green-100 text-green-600' : 'bg-blue-100 text-blue-600'),\n                                                                                                children: subSection.type === 'form' ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowDownTrayIcon_ArrowLeftIcon_ArrowRightIcon_BookOpenIcon_ChevronDownIcon_ChevronUpIcon_CloudArrowUpIcon_DocumentIcon_DocumentTextIcon_ExclamationTriangleIcon_EyeIcon_PlusIcon_ShieldCheckIcon_SpeakerWaveIcon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                                                                                    className: \"h-3 w-3\"\n                                                                                                }, void 0, false, {\n                                                                                                    fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-2/page.tsx\",\n                                                                                                    lineNumber: 1288,\n                                                                                                    columnNumber: 41\n                                                                                                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowDownTrayIcon_ArrowLeftIcon_ArrowRightIcon_BookOpenIcon_ChevronDownIcon_ChevronUpIcon_CloudArrowUpIcon_DocumentIcon_DocumentTextIcon_ExclamationTriangleIcon_EyeIcon_PlusIcon_ShieldCheckIcon_SpeakerWaveIcon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                                                                    className: \"h-3 w-3\"\n                                                                                                }, void 0, false, {\n                                                                                                    fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-2/page.tsx\",\n                                                                                                    lineNumber: 1290,\n                                                                                                    columnNumber: 41\n                                                                                                }, this)\n                                                                                            }, void 0, false, {\n                                                                                                fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-2/page.tsx\",\n                                                                                                lineNumber: 1282,\n                                                                                                columnNumber: 37\n                                                                                            }, this),\n                                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                                                                className: \"font-gotham-rounded text-base font-semibold text-gray-900\",\n                                                                                                children: [\n                                                                                                    String.fromCharCode(65 + index),\n                                                                                                    \".\",\n                                                                                                    subIndex + 1,\n                                                                                                    \" \",\n                                                                                                    subSection.title\n                                                                                                ]\n                                                                                            }, void 0, true, {\n                                                                                                fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-2/page.tsx\",\n                                                                                                lineNumber: 1293,\n                                                                                                columnNumber: 37\n                                                                                            }, this),\n                                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                                className: \"px-2 py-0.5 text-xs font-medium rounded-full \".concat(subSection.type === 'form' ? 'bg-green-100 text-green-800' : 'bg-blue-100 text-blue-800'),\n                                                                                                children: subSection.type === 'form' ? 'Form' : 'Upload'\n                                                                                            }, void 0, false, {\n                                                                                                fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-2/page.tsx\",\n                                                                                                lineNumber: 1296,\n                                                                                                columnNumber: 37\n                                                                                            }, this)\n                                                                                        ]\n                                                                                    }, void 0, true, {\n                                                                                        fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-2/page.tsx\",\n                                                                                        lineNumber: 1281,\n                                                                                        columnNumber: 35\n                                                                                    }, this),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                        className: \"font-gotham text-gray-600 text-sm\",\n                                                                                        children: subSection.description\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-2/page.tsx\",\n                                                                                        lineNumber: 1304,\n                                                                                        columnNumber: 35\n                                                                                    }, this),\n                                                                                    subSection.type === 'form' && subSection.formUrl && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                        className: \"font-gotham text-xs text-blue-600 mt-1\",\n                                                                                        children: [\n                                                                                            \"URL: \",\n                                                                                            subSection.formUrl\n                                                                                        ]\n                                                                                    }, void 0, true, {\n                                                                                        fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-2/page.tsx\",\n                                                                                        lineNumber: 1308,\n                                                                                        columnNumber: 37\n                                                                                    }, this)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-2/page.tsx\",\n                                                                                lineNumber: 1280,\n                                                                                columnNumber: 33\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"flex items-center space-x-2 ml-4\",\n                                                                                children: [\n                                                                                    subSection.type === 'form' ? /* Form button - for form type subsections */ /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                                        onClick: ()=>window.open(subSection.formUrl, '_blank'),\n                                                                                        className: \"flex items-center space-x-2 px-3 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors text-sm font-gotham font-medium\",\n                                                                                        children: [\n                                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowDownTrayIcon_ArrowLeftIcon_ArrowRightIcon_BookOpenIcon_ChevronDownIcon_ChevronUpIcon_CloudArrowUpIcon_DocumentIcon_DocumentTextIcon_ExclamationTriangleIcon_EyeIcon_PlusIcon_ShieldCheckIcon_SpeakerWaveIcon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                                                                                className: \"h-4 w-4\"\n                                                                                            }, void 0, false, {\n                                                                                                fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-2/page.tsx\",\n                                                                                                lineNumber: 1320,\n                                                                                                columnNumber: 39\n                                                                                            }, this),\n                                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                                children: \"Isi Form\"\n                                                                                            }, void 0, false, {\n                                                                                                fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-2/page.tsx\",\n                                                                                                lineNumber: 1321,\n                                                                                                columnNumber: 39\n                                                                                            }, this)\n                                                                                        ]\n                                                                                    }, void 0, true, {\n                                                                                        fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-2/page.tsx\",\n                                                                                        lineNumber: 1316,\n                                                                                        columnNumber: 37\n                                                                                    }, this) : /* Upload and View buttons - for upload type subsections */ /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                                                        children: [\n                                                                                            (user.role === 'admin_super' || user.role === 'admin_biasa' || user.role === 'scoopers') && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                                                onClick: ()=>handleUpload(subSection.id),\n                                                                                                className: \"flex items-center space-x-2 px-3 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors text-sm font-gotham font-medium\",\n                                                                                                children: [\n                                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowDownTrayIcon_ArrowLeftIcon_ArrowRightIcon_BookOpenIcon_ChevronDownIcon_ChevronUpIcon_CloudArrowUpIcon_DocumentIcon_DocumentTextIcon_ExclamationTriangleIcon_EyeIcon_PlusIcon_ShieldCheckIcon_SpeakerWaveIcon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                                                                        className: \"h-4 w-4\"\n                                                                                                    }, void 0, false, {\n                                                                                                        fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-2/page.tsx\",\n                                                                                                        lineNumber: 1332,\n                                                                                                        columnNumber: 43\n                                                                                                    }, this),\n                                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                                        children: \"Upload\"\n                                                                                                    }, void 0, false, {\n                                                                                                        fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-2/page.tsx\",\n                                                                                                        lineNumber: 1333,\n                                                                                                        columnNumber: 43\n                                                                                                    }, this)\n                                                                                                ]\n                                                                                            }, void 0, true, {\n                                                                                                fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-2/page.tsx\",\n                                                                                                lineNumber: 1328,\n                                                                                                columnNumber: 41\n                                                                                            }, this),\n                                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                                                onClick: ()=>handleViewFile(subSection.id),\n                                                                                                className: \"flex items-center space-x-2 px-3 py-2 rounded-lg transition-colors text-sm font-gotham font-medium \".concat(hasFileInSection(subSection.id) ? 'bg-green-600 text-white hover:bg-green-700' : 'bg-gray-400 text-white hover:bg-gray-500'),\n                                                                                                children: [\n                                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowDownTrayIcon_ArrowLeftIcon_ArrowRightIcon_BookOpenIcon_ChevronDownIcon_ChevronUpIcon_CloudArrowUpIcon_DocumentIcon_DocumentTextIcon_ExclamationTriangleIcon_EyeIcon_PlusIcon_ShieldCheckIcon_SpeakerWaveIcon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                                                                        className: \"h-4 w-4\"\n                                                                                                    }, void 0, false, {\n                                                                                                        fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-2/page.tsx\",\n                                                                                                        lineNumber: 1346,\n                                                                                                        columnNumber: 41\n                                                                                                    }, this),\n                                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                                        children: hasFileInSection(subSection.id) ? 'View File' : 'No File'\n                                                                                                    }, void 0, false, {\n                                                                                                        fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-2/page.tsx\",\n                                                                                                        lineNumber: 1347,\n                                                                                                        columnNumber: 41\n                                                                                                    }, this)\n                                                                                                ]\n                                                                                            }, void 0, true, {\n                                                                                                fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-2/page.tsx\",\n                                                                                                lineNumber: 1338,\n                                                                                                columnNumber: 39\n                                                                                            }, this)\n                                                                                        ]\n                                                                                    }, void 0, true),\n                                                                                    canDeleteSubSection(subSection.id) && (user.role === 'admin_super' || user.role === 'admin_biasa') && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                                        onClick: ()=>handleDeleteRequest('subsection', subSection.id, subSection.title),\n                                                                                        className: \"flex items-center space-x-2 px-3 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700 transition-colors text-sm font-gotham font-medium\",\n                                                                                        title: \"Hapus Sub-Section\",\n                                                                                        children: [\n                                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowDownTrayIcon_ArrowLeftIcon_ArrowRightIcon_BookOpenIcon_ChevronDownIcon_ChevronUpIcon_CloudArrowUpIcon_DocumentIcon_DocumentTextIcon_ExclamationTriangleIcon_EyeIcon_PlusIcon_ShieldCheckIcon_SpeakerWaveIcon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                                                                className: \"h-4 w-4\"\n                                                                                            }, void 0, false, {\n                                                                                                fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-2/page.tsx\",\n                                                                                                lineNumber: 1359,\n                                                                                                columnNumber: 39\n                                                                                            }, this),\n                                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                                children: \"Delete\"\n                                                                                            }, void 0, false, {\n                                                                                                fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-2/page.tsx\",\n                                                                                                lineNumber: 1360,\n                                                                                                columnNumber: 39\n                                                                                            }, this)\n                                                                                        ]\n                                                                                    }, void 0, true, {\n                                                                                        fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-2/page.tsx\",\n                                                                                        lineNumber: 1354,\n                                                                                        columnNumber: 37\n                                                                                    }, this),\n                                                                                    subSection.id === 'formulir-risk-assessment' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                                                        children: [\n                                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                                                onClick: ()=>window.open('/forms/risk-assessment', '_blank'),\n                                                                                                className: \"flex items-center space-x-2 px-3 py-2 bg-orange-600 text-white rounded-lg hover:bg-orange-700 transition-colors text-sm font-gotham font-medium\",\n                                                                                                title: \"Buka Formulir Risk Assessment\",\n                                                                                                children: [\n                                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowDownTrayIcon_ArrowLeftIcon_ArrowRightIcon_BookOpenIcon_ChevronDownIcon_ChevronUpIcon_CloudArrowUpIcon_DocumentIcon_DocumentTextIcon_ExclamationTriangleIcon_EyeIcon_PlusIcon_ShieldCheckIcon_SpeakerWaveIcon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                                                                                        className: \"h-4 w-4\"\n                                                                                                    }, void 0, false, {\n                                                                                                        fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-2/page.tsx\",\n                                                                                                        lineNumber: 1372,\n                                                                                                        columnNumber: 41\n                                                                                                    }, this),\n                                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                                        children: \"Isi Formulir\"\n                                                                                                    }, void 0, false, {\n                                                                                                        fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-2/page.tsx\",\n                                                                                                        lineNumber: 1373,\n                                                                                                        columnNumber: 41\n                                                                                                    }, this)\n                                                                                                ]\n                                                                                            }, void 0, true, {\n                                                                                                fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-2/page.tsx\",\n                                                                                                lineNumber: 1367,\n                                                                                                columnNumber: 39\n                                                                                            }, this),\n                                                                                            (user.role === 'admin_super' || user.role === 'admin_biasa') && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                                                onClick: ()=>window.open('/forms/risk-assessment/view', '_blank'),\n                                                                                                className: \"flex items-center space-x-2 px-3 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors text-sm font-gotham font-medium\",\n                                                                                                title: \"Lihat Formulir yang Telah Diisi\",\n                                                                                                children: [\n                                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowDownTrayIcon_ArrowLeftIcon_ArrowRightIcon_BookOpenIcon_ChevronDownIcon_ChevronUpIcon_CloudArrowUpIcon_DocumentIcon_DocumentTextIcon_ExclamationTriangleIcon_EyeIcon_PlusIcon_ShieldCheckIcon_SpeakerWaveIcon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                                                                        className: \"h-4 w-4\"\n                                                                                                    }, void 0, false, {\n                                                                                                        fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-2/page.tsx\",\n                                                                                                        lineNumber: 1383,\n                                                                                                        columnNumber: 43\n                                                                                                    }, this),\n                                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                                        children: \"Lihat Formulir\"\n                                                                                                    }, void 0, false, {\n                                                                                                        fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-2/page.tsx\",\n                                                                                                        lineNumber: 1384,\n                                                                                                        columnNumber: 43\n                                                                                                    }, this)\n                                                                                                ]\n                                                                                            }, void 0, true, {\n                                                                                                fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-2/page.tsx\",\n                                                                                                lineNumber: 1378,\n                                                                                                columnNumber: 41\n                                                                                            }, this)\n                                                                                        ]\n                                                                                    }, void 0, true)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-2/page.tsx\",\n                                                                                lineNumber: 1313,\n                                                                                columnNumber: 33\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-2/page.tsx\",\n                                                                        lineNumber: 1279,\n                                                                        columnNumber: 31\n                                                                    }, this)\n                                                                }, subSection.id, false, {\n                                                                    fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-2/page.tsx\",\n                                                                    lineNumber: 1275,\n                                                                    columnNumber: 29\n                                                                }, this)),\n                                                            (user.role === 'admin_super' || user.role === 'admin_biasa') && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"border-2 border-dashed border-gray-300 rounded-lg p-4 text-center hover:border-blue-400 hover:bg-blue-50 transition-all duration-200\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                        onClick: ()=>handleAddSubSection(subDoc.id),\n                                                                        className: \"flex items-center justify-center space-x-2 mx-auto px-3 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors font-gotham font-medium text-sm\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowDownTrayIcon_ArrowLeftIcon_ArrowRightIcon_BookOpenIcon_ChevronDownIcon_ChevronUpIcon_CloudArrowUpIcon_DocumentIcon_DocumentTextIcon_ExclamationTriangleIcon_EyeIcon_PlusIcon_ShieldCheckIcon_SpeakerWaveIcon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                                                                className: \"h-4 w-4\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-2/page.tsx\",\n                                                                                lineNumber: 1401,\n                                                                                columnNumber: 33\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                children: \"Tambah Sub-Section\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-2/page.tsx\",\n                                                                                lineNumber: 1402,\n                                                                                columnNumber: 33\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-2/page.tsx\",\n                                                                        lineNumber: 1397,\n                                                                        columnNumber: 31\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        className: \"font-gotham text-xs text-gray-500 mt-1\",\n                                                                        children: [\n                                                                            \"Tambahkan sub-section baru untuk \",\n                                                                            subDoc.title\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-2/page.tsx\",\n                                                                        lineNumber: 1404,\n                                                                        columnNumber: 31\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-2/page.tsx\",\n                                                                lineNumber: 1396,\n                                                                columnNumber: 29\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-2/page.tsx\",\n                                                        lineNumber: 1273,\n                                                        columnNumber: 25\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-2/page.tsx\",\n                                                    lineNumber: 1272,\n                                                    columnNumber: 23\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-2/page.tsx\",\n                                                lineNumber: 1271,\n                                                columnNumber: 21\n                                            }, this),\n                                            isExpanded && getAllSubSections(subDoc.id).length === 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"border-t border-gray-200 bg-gray-50 p-6\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"bg-white border border-gray-200 rounded-lg p-4\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center justify-between\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex-1\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                                        className: \"font-gotham-rounded text-base font-semibold text-gray-900 mb-1\",\n                                                                        children: [\n                                                                            String.fromCharCode(65 + index),\n                                                                            \".1 Upload Dokumen \",\n                                                                            subDoc.title\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-2/page.tsx\",\n                                                                        lineNumber: 1420,\n                                                                        columnNumber: 29\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        className: \"font-gotham text-gray-600 text-sm\",\n                                                                        children: [\n                                                                            \"Upload file dokumen untuk \",\n                                                                            subDoc.title\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-2/page.tsx\",\n                                                                        lineNumber: 1423,\n                                                                        columnNumber: 29\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-2/page.tsx\",\n                                                                lineNumber: 1419,\n                                                                columnNumber: 27\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center space-x-2 ml-4\",\n                                                                children: [\n                                                                    (user.role === 'admin_super' || user.role === 'admin_biasa' || user.role === 'scoopers') && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                        onClick: ()=>handleUpload(subDoc.id),\n                                                                        className: \"flex items-center space-x-2 px-3 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors text-sm font-gotham font-medium\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowDownTrayIcon_ArrowLeftIcon_ArrowRightIcon_BookOpenIcon_ChevronDownIcon_ChevronUpIcon_CloudArrowUpIcon_DocumentIcon_DocumentTextIcon_ExclamationTriangleIcon_EyeIcon_PlusIcon_ShieldCheckIcon_SpeakerWaveIcon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                                                className: \"h-4 w-4\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-2/page.tsx\",\n                                                                                lineNumber: 1434,\n                                                                                columnNumber: 33\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                children: \"Upload\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-2/page.tsx\",\n                                                                                lineNumber: 1435,\n                                                                                columnNumber: 33\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-2/page.tsx\",\n                                                                        lineNumber: 1430,\n                                                                        columnNumber: 31\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                        onClick: ()=>handleViewFile(subDoc.id),\n                                                                        className: \"flex items-center space-x-2 px-3 py-2 rounded-lg transition-colors text-sm font-gotham font-medium \".concat(hasFileInSection(subDoc.id) ? 'bg-green-600 text-white hover:bg-green-700' : 'bg-gray-400 text-white hover:bg-gray-500'),\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowDownTrayIcon_ArrowLeftIcon_ArrowRightIcon_BookOpenIcon_ChevronDownIcon_ChevronUpIcon_CloudArrowUpIcon_DocumentIcon_DocumentTextIcon_ExclamationTriangleIcon_EyeIcon_PlusIcon_ShieldCheckIcon_SpeakerWaveIcon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                                                className: \"h-4 w-4\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-2/page.tsx\",\n                                                                                lineNumber: 1448,\n                                                                                columnNumber: 31\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                children: hasFileInSection(subDoc.id) ? 'View File' : 'No File'\n                                                                            }, void 0, false, {\n                                                                                fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-2/page.tsx\",\n                                                                                lineNumber: 1449,\n                                                                                columnNumber: 31\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-2/page.tsx\",\n                                                                        lineNumber: 1440,\n                                                                        columnNumber: 29\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-2/page.tsx\",\n                                                                lineNumber: 1427,\n                                                                columnNumber: 27\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-2/page.tsx\",\n                                                        lineNumber: 1418,\n                                                        columnNumber: 25\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-2/page.tsx\",\n                                                    lineNumber: 1417,\n                                                    columnNumber: 23\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-2/page.tsx\",\n                                                lineNumber: 1416,\n                                                columnNumber: 21\n                                            }, this)\n                                        ]\n                                    }, subDoc.id, true, {\n                                        fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-2/page.tsx\",\n                                        lineNumber: 1219,\n                                        columnNumber: 17\n                                    }, this);\n                                }),\n                                (user.role === 'admin_super' || user.role === 'admin_biasa') && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"border-2 border-dashed border-gray-300 rounded-xl p-6 text-center hover:border-blue-400 hover:bg-blue-50 transition-all duration-200\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: handleAddMainSection,\n                                            className: \"flex items-center justify-center space-x-2 mx-auto px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors font-gotham font-medium\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowDownTrayIcon_ArrowLeftIcon_ArrowRightIcon_BookOpenIcon_ChevronDownIcon_ChevronUpIcon_CloudArrowUpIcon_DocumentIcon_DocumentTextIcon_ExclamationTriangleIcon_EyeIcon_PlusIcon_ShieldCheckIcon_SpeakerWaveIcon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                                    className: \"h-5 w-5\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-2/page.tsx\",\n                                                    lineNumber: 1467,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    children: \"Tambah Section Baru\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-2/page.tsx\",\n                                                    lineNumber: 1468,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-2/page.tsx\",\n                                            lineNumber: 1463,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"font-gotham text-sm text-gray-500 mt-2\",\n                                            children: \"Tambahkan section baru untuk Prosedur dan Instruksi Kerja\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-2/page.tsx\",\n                                            lineNumber: 1470,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-2/page.tsx\",\n                                    lineNumber: 1462,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-2/page.tsx\",\n                            lineNumber: 1213,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"mt-12 bg-blue-50 rounded-xl p-6\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-start space-x-3\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"p-2 bg-blue-100 rounded-lg\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowDownTrayIcon_ArrowLeftIcon_ArrowRightIcon_BookOpenIcon_ChevronDownIcon_ChevronUpIcon_CloudArrowUpIcon_DocumentIcon_DocumentTextIcon_ExclamationTriangleIcon_EyeIcon_PlusIcon_ShieldCheckIcon_SpeakerWaveIcon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                            className: \"h-5 w-5 text-blue-600\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-2/page.tsx\",\n                                            lineNumber: 1481,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-2/page.tsx\",\n                                        lineNumber: 1480,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"font-gotham-rounded text-lg font-semibold text-blue-900 mb-2\",\n                                                children: \"Informasi Dokumen\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-2/page.tsx\",\n                                                lineNumber: 1484,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"font-gotham text-blue-800 text-sm leading-relaxed\",\n                                                children: \"Dokumen Prosedur dan Instruksi Kerja terdiri dari 4 sub-dokumen utama yang mengatur prosedur operasional standar dan instruksi kerja. Setiap sub-dokumen memiliki fokus khusus dalam mendukung implementasi proses bisnis yang efektif dan efisien.\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-2/page.tsx\",\n                                                lineNumber: 1487,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-2/page.tsx\",\n                                        lineNumber: 1483,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-2/page.tsx\",\n                                lineNumber: 1479,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-2/page.tsx\",\n                            lineNumber: 1478,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-2/page.tsx\",\n                    lineNumber: 1176,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-2/page.tsx\",\n                lineNumber: 1175,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(FileManagerModal, {\n                isOpen: showFileManager,\n                onClose: ()=>setShowFileManager(false),\n                sectionId: currentSection,\n                sectionTitle: getSectionTitle(currentSection),\n                userRole: user.role\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-2/page.tsx\",\n                lineNumber: 1499,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(AddSectionModal, {\n                isOpen: showAddSection,\n                onClose: ()=>setShowAddSection(false),\n                onSave: handleSaveNewMainSection,\n                subDocTitle: \"Prosedur dan Instruksi Kerja\"\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-2/page.tsx\",\n                lineNumber: 1508,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(AddSubSectionModal, {\n                isOpen: showAddSubSection,\n                onClose: ()=>setShowAddSubSection(false),\n                onSave: handleSaveNewSubSection,\n                sectionTitle: getSectionTitle(currentSubDocId)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-2/page.tsx\",\n                lineNumber: 1516,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(DeleteConfirmModal, {\n                isOpen: showDeleteConfirm,\n                onClose: ()=>setShowDeleteConfirm(false),\n                onConfirm: handleConfirmDelete,\n                title: deleteTarget.title,\n                type: deleteTarget.type\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-2/page.tsx\",\n                lineNumber: 1524,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-2/page.tsx\",\n        lineNumber: 1174,\n        columnNumber: 5\n    }, this);\n}\n_s3(PedomanPeraturanPage, \"L9/CUVHXNpwTygVSFu0MTNxOmPw=\", false, function() {\n    return [\n        _store_auth__WEBPACK_IMPORTED_MODULE_2__.useAuth,\n        _contexts_ProgramContext__WEBPACK_IMPORTED_MODULE_3__.useProgramDocument\n    ];\n});\n_c5 = PedomanPeraturanPage;\nvar _c, _c1, _c2, _c3, _c4, _c5;\n$RefreshReg$(_c, \"FilePreviewModal\");\n$RefreshReg$(_c1, \"FileManagerModal\");\n$RefreshReg$(_c2, \"AddSectionModal\");\n$RefreshReg$(_c3, \"AddSubSectionModal\");\n$RefreshReg$(_c4, \"DeleteConfirmModal\");\n$RefreshReg$(_c5, \"PedomanPeraturanPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/dokumen/doc-2/page.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/store/progress.ts":
/*!*******************************!*\
  !*** ./src/store/progress.ts ***!
  \*******************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useProgressStore: () => (/* binding */ useProgressStore)\n/* harmony export */ });\n/* harmony import */ var zustand__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! zustand */ \"(app-pages-browser)/./node_modules/zustand/esm/index.mjs\");\n\n// Mock data untuk progress tracking\nconst MOCK_PROGRESS_DATA = [\n    {\n        id: 'doc-1',\n        title: 'Pedoman Peraturan Perusahaan',\n        description: 'Dokumen pedoman dan peraturan perusahaan terkait anti penyuapan',\n        sections: [\n            {\n                id: 'doc-1-section-1',\n                title: 'Kebijakan Anti Penyuapan',\n                description: 'Kebijakan dasar perusahaan terkait anti penyuapan',\n                totalSubsections: 5,\n                completedSubsections: 5,\n                progressPercentage: 100,\n                status: 'completed',\n                lastUpdated: '2024-01-15T10:30:00Z',\n                updatedBy: 'Admin Super'\n            },\n            {\n                id: 'doc-1-section-2',\n                title: 'Prosedur Pelaporan',\n                description: 'Prosedur pelaporan dugaan penyuapan',\n                totalSubsections: 4,\n                completedSubsections: 3,\n                progressPercentage: 75,\n                status: 'in_progress',\n                lastUpdated: '2024-01-14T15:20:00Z',\n                updatedBy: 'Admin Biasa'\n            },\n            {\n                id: 'doc-1-section-3',\n                title: 'Sanksi dan Konsekuensi',\n                description: 'Sanksi untuk pelanggaran kebijakan anti penyuapan',\n                totalSubsections: 3,\n                completedSubsections: 1,\n                progressPercentage: 33,\n                status: 'in_progress',\n                lastUpdated: '2024-01-13T09:15:00Z',\n                updatedBy: 'Scoopers'\n            },\n            {\n                id: 'doc-1-section-4',\n                title: 'Training dan Awareness',\n                description: 'Program pelatihan dan kesadaran anti penyuapan',\n                totalSubsections: 6,\n                completedSubsections: 0,\n                progressPercentage: 0,\n                status: 'not_started',\n                lastUpdated: '',\n                updatedBy: ''\n            }\n        ],\n        totalSections: 4,\n        completedSections: 1,\n        overallProgress: 52,\n        status: 'in_progress',\n        lastUpdated: '2024-01-15T10:30:00Z'\n    },\n    {\n        id: 'doc-2',\n        title: 'Prosedur dan Instruksi Kerja',\n        description: 'Prosedur operasional standar dan instruksi kerja',\n        sections: [\n            {\n                id: 'doc-2-section-1',\n                title: 'SOP Procurement',\n                description: 'Standard Operating Procedure untuk procurement',\n                totalSubsections: 8,\n                completedSubsections: 6,\n                progressPercentage: 75,\n                status: 'in_progress',\n                lastUpdated: '2024-01-14T14:45:00Z',\n                updatedBy: 'Admin Super'\n            },\n            {\n                id: 'doc-2-section-2',\n                title: 'SOP Vendor Management',\n                description: 'Prosedur pengelolaan vendor dan supplier',\n                totalSubsections: 5,\n                completedSubsections: 2,\n                progressPercentage: 40,\n                status: 'in_progress',\n                lastUpdated: '2024-01-12T11:30:00Z',\n                updatedBy: 'Admin Biasa'\n            },\n            {\n                id: 'doc-2-section-3',\n                title: 'SOP Due Diligence',\n                description: 'Prosedur due diligence untuk mitra bisnis',\n                totalSubsections: 7,\n                completedSubsections: 0,\n                progressPercentage: 0,\n                status: 'not_started',\n                lastUpdated: '',\n                updatedBy: ''\n            }\n        ],\n        totalSections: 3,\n        completedSections: 0,\n        overallProgress: 38,\n        status: 'in_progress',\n        lastUpdated: '2024-01-14T14:45:00Z'\n    },\n    {\n        id: 'doc-3',\n        title: 'Evidence Mapping Klausul',\n        description: 'Pemetaan evidence untuk setiap klausul',\n        sections: [\n            {\n                id: 'doc-3-section-1',\n                title: 'Klausul ISO 37001',\n                description: 'Mapping evidence untuk klausul ISO 37001',\n                totalSubsections: 10,\n                completedSubsections: 8,\n                progressPercentage: 80,\n                status: 'in_progress',\n                lastUpdated: '2024-01-15T16:20:00Z',\n                updatedBy: 'Scoopers'\n            },\n            {\n                id: 'doc-3-section-2',\n                title: 'Klausul Regulasi Lokal',\n                description: 'Mapping evidence untuk regulasi lokal',\n                totalSubsections: 6,\n                completedSubsections: 3,\n                progressPercentage: 50,\n                status: 'in_progress',\n                lastUpdated: '2024-01-13T13:10:00Z',\n                updatedBy: 'Scoopers'\n            }\n        ],\n        totalSections: 2,\n        completedSections: 0,\n        overallProgress: 65,\n        status: 'in_progress',\n        lastUpdated: '2024-01-15T16:20:00Z'\n    },\n    {\n        id: 'doc-4',\n        title: 'Fungsi Kepatuhan Anti Penyuapan',\n        description: 'Dokumen fungsi kepatuhan dan monitoring',\n        sections: [\n            {\n                id: 'doc-4-section-1',\n                title: 'Struktur Organisasi Kepatuhan',\n                description: 'Struktur dan tanggung jawab fungsi kepatuhan',\n                totalSubsections: 4,\n                completedSubsections: 4,\n                progressPercentage: 100,\n                status: 'completed',\n                lastUpdated: '2024-01-10T12:00:00Z',\n                updatedBy: 'Admin Super'\n            },\n            {\n                id: 'doc-4-section-2',\n                title: 'Monitoring dan Audit',\n                description: 'Prosedur monitoring dan audit internal',\n                totalSubsections: 5,\n                completedSubsections: 2,\n                progressPercentage: 40,\n                status: 'in_progress',\n                lastUpdated: '2024-01-11T09:30:00Z',\n                updatedBy: 'Admin Biasa'\n            },\n            {\n                id: 'doc-4-section-3',\n                title: 'Pelaporan dan Eskalasi',\n                description: 'Prosedur pelaporan dan eskalasi masalah',\n                totalSubsections: 3,\n                completedSubsections: 0,\n                progressPercentage: 0,\n                status: 'not_started',\n                lastUpdated: '',\n                updatedBy: ''\n            }\n        ],\n        totalSections: 3,\n        completedSections: 1,\n        overallProgress: 47,\n        status: 'in_progress',\n        lastUpdated: '2024-01-11T09:30:00Z'\n    }\n];\nconst useProgressStore = (0,zustand__WEBPACK_IMPORTED_MODULE_0__.create)((set, get)=>({\n        documents: [\n            {\n                id: 'doc-1',\n                title: 'Pedoman Peraturan Perusahaan',\n                description: 'Dokumen pedoman dan peraturan perusahaan terkait anti penyuapan',\n                sections: [],\n                totalSections: 0,\n                completedSections: 0,\n                overallProgress: 0,\n                status: 'not_started',\n                lastUpdated: new Date().toISOString()\n            },\n            {\n                id: 'doc-2',\n                title: 'Prosedur dan Instruksi Kerja',\n                description: 'Prosedur operasional standar dan instruksi kerja',\n                sections: [],\n                totalSections: 0,\n                completedSections: 0,\n                overallProgress: 0,\n                status: 'not_started',\n                lastUpdated: new Date().toISOString()\n            },\n            {\n                id: 'doc-3',\n                title: 'Evidence Mapping Klausul',\n                description: 'Pemetaan evidence untuk setiap klausul',\n                sections: [],\n                totalSections: 0,\n                completedSections: 0,\n                overallProgress: 0,\n                status: 'not_started',\n                lastUpdated: new Date().toISOString()\n            },\n            {\n                id: 'doc-4',\n                title: 'Fungsi Kepatuhan Anti Penyuapan',\n                description: 'Dokumen fungsi kepatuhan dan monitoring',\n                sections: [],\n                totalSections: 0,\n                completedSections: 0,\n                overallProgress: 0,\n                status: 'not_started',\n                lastUpdated: new Date().toISOString()\n            }\n        ],\n        isLoading: false,\n        getProgressSummary: (userRole)=>{\n            const { documents } = get();\n            // Filter documents based on user role\n            const accessibleDocuments = documents.filter((doc)=>{\n                if (userRole === 'admin_super' || userRole === 'admin_biasa') {\n                    return true; // Can access all documents\n                }\n                if (userRole === 'scoopers') {\n                    return [\n                        'doc-1',\n                        'doc-2',\n                        'doc-3'\n                    ].includes(doc.id); // Cannot access doc-4\n                }\n                return false; // Other roles cannot access progress tracking\n            });\n            const totalDocuments = accessibleDocuments.length;\n            const completedDocuments = accessibleDocuments.filter((doc)=>doc.status === 'completed').length;\n            const inProgressDocuments = accessibleDocuments.filter((doc)=>doc.status === 'in_progress').length;\n            const notStartedDocuments = accessibleDocuments.filter((doc)=>doc.status === 'not_started').length;\n            const overallProgress = totalDocuments > 0 ? Math.round(accessibleDocuments.reduce((sum, doc)=>sum + doc.overallProgress, 0) / totalDocuments) : 0;\n            return {\n                totalDocuments,\n                completedDocuments,\n                inProgressDocuments,\n                notStartedDocuments,\n                overallProgress,\n                documents: accessibleDocuments\n            };\n        },\n        getProgressStats: (userRole)=>{\n            const { documents } = get();\n            // Filter documents based on user role\n            const accessibleDocuments = documents.filter((doc)=>{\n                if (userRole === 'admin_super' || userRole === 'admin_biasa') {\n                    return true;\n                }\n                if (userRole === 'scoopers') {\n                    return [\n                        'doc-1',\n                        'doc-2',\n                        'doc-3'\n                    ].includes(doc.id);\n                }\n                return false;\n            });\n            const totalSections = accessibleDocuments.reduce((sum, doc)=>sum + doc.totalSections, 0);\n            const completedSections = accessibleDocuments.reduce((sum, doc)=>sum + doc.completedSections, 0);\n            const totalSubsections = accessibleDocuments.reduce((sum, doc)=>sum + doc.sections.reduce((sectionSum, section)=>sectionSum + section.totalSubsections, 0), 0);\n            const completedSubsections = accessibleDocuments.reduce((sum, doc)=>sum + doc.sections.reduce((sectionSum, section)=>sectionSum + section.completedSubsections, 0), 0);\n            const averageProgress = totalSections > 0 ? Math.round(accessibleDocuments.reduce((sum, doc)=>sum + doc.overallProgress, 0) / accessibleDocuments.length) : 0;\n            return {\n                totalSections,\n                completedSections,\n                totalSubsections,\n                completedSubsections,\n                averageProgress\n            };\n        },\n        getDocumentProgress: (documentId)=>{\n            const { documents } = get();\n            return documents.find((doc)=>doc.id === documentId);\n        },\n        updateSectionProgress: (documentId, sectionId, completedSubsections, updatedBy)=>{\n            set((state)=>({\n                    documents: state.documents.map((doc)=>{\n                        if (doc.id === documentId) {\n                            const updatedSections = doc.sections.map((section)=>{\n                                if (section.id === sectionId) {\n                                    const progressPercentage = Math.round(completedSubsections / section.totalSubsections * 100);\n                                    const status = progressPercentage === 100 ? 'completed' : progressPercentage > 0 ? 'in_progress' : 'not_started';\n                                    return {\n                                        ...section,\n                                        completedSubsections,\n                                        progressPercentage,\n                                        status,\n                                        lastUpdated: new Date().toISOString(),\n                                        updatedBy\n                                    };\n                                }\n                                return section;\n                            });\n                            // Recalculate document progress\n                            const totalSubsections = updatedSections.reduce((sum, section)=>sum + section.totalSubsections, 0);\n                            const completedSubsectionsTotal = updatedSections.reduce((sum, section)=>sum + section.completedSubsections, 0);\n                            const overallProgress = totalSubsections > 0 ? Math.round(completedSubsectionsTotal / totalSubsections * 100) : 0;\n                            const completedSections = updatedSections.filter((section)=>section.status === 'completed').length;\n                            const docStatus = overallProgress === 100 ? 'completed' : overallProgress > 0 ? 'in_progress' : 'not_started';\n                            return {\n                                ...doc,\n                                sections: updatedSections,\n                                completedSections,\n                                overallProgress,\n                                status: docStatus,\n                                lastUpdated: new Date().toISOString()\n                            };\n                        }\n                        return doc;\n                    })\n                }));\n        },\n        // Add or update section in progress tracking\n        addOrUpdateSection: function(documentId, sectionId, sectionTitle) {\n            let totalSubsections = arguments.length > 3 && arguments[3] !== void 0 ? arguments[3] : 0;\n            set((state)=>({\n                    documents: state.documents.map((doc)=>{\n                        if (doc.id === documentId) {\n                            const existingSectionIndex = doc.sections.findIndex((s)=>s.id === sectionId);\n                            if (existingSectionIndex >= 0) {\n                                // Update existing section\n                                const updatedSections = [\n                                    ...doc.sections\n                                ];\n                                updatedSections[existingSectionIndex] = {\n                                    ...updatedSections[existingSectionIndex],\n                                    title: sectionTitle,\n                                    totalSubsections,\n                                    lastUpdated: new Date().toISOString()\n                                };\n                                return {\n                                    ...doc,\n                                    sections: updatedSections,\n                                    totalSections: updatedSections.length,\n                                    lastUpdated: new Date().toISOString()\n                                };\n                            } else {\n                                // Add new section\n                                const newSection = {\n                                    id: sectionId,\n                                    title: sectionTitle,\n                                    description: '',\n                                    totalSubsections,\n                                    completedSubsections: 0,\n                                    progressPercentage: 0,\n                                    status: 'not_started',\n                                    lastUpdated: new Date().toISOString(),\n                                    updatedBy: ''\n                                };\n                                return {\n                                    ...doc,\n                                    sections: [\n                                        ...doc.sections,\n                                        newSection\n                                    ],\n                                    totalSections: doc.sections.length + 1,\n                                    lastUpdated: new Date().toISOString()\n                                };\n                            }\n                        }\n                        return doc;\n                    })\n                }));\n        },\n        refreshProgress: ()=>{\n            set({\n                isLoading: true\n            });\n            // Simulate API call\n            setTimeout(()=>{\n                set({\n                    isLoading: false\n                });\n            }, 1000);\n        }\n    }));\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/store/progress.ts\n"));

/***/ })

});