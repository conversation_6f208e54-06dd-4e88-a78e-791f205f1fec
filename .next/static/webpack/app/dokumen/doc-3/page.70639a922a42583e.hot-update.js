"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/dokumen/doc-3/page",{

/***/ "(app-pages-browser)/./src/components/debug/AuthDebugPanel.tsx":
/*!*************************************************!*\
  !*** ./src/components/debug/AuthDebugPanel.tsx ***!
  \*************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AuthDebugPanel: () => (/* binding */ AuthDebugPanel),\n/* harmony export */   useAuthDebug: () => (/* binding */ useAuthDebug)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _store_auth__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/store/auth */ \"(app-pages-browser)/./src/store/auth.ts\");\n\nvar _s = $RefreshSig$(), _s1 = $RefreshSig$();\n\n\nfunction AuthDebugPanel(param) {\n    let { isVisible = false } = param;\n    _s();\n    const { user, isAuthenticated, hasRole } = (0,_store_auth__WEBPACK_IMPORTED_MODULE_2__.useAuth)();\n    const [debugInfo, setDebugInfo] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({});\n    const [isExpanded, setIsExpanded] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"AuthDebugPanel.useEffect\": ()=>{\n            // Collect debug information\n            const collectDebugInfo = {\n                \"AuthDebugPanel.useEffect.collectDebugInfo\": ()=>{\n                    const savedUser = localStorage.getItem('smap_user');\n                    const configUsers = localStorage.getItem('config-users');\n                    let parsedSavedUser = null;\n                    let parsedConfigUsers = null;\n                    try {\n                        parsedSavedUser = savedUser ? JSON.parse(savedUser) : null;\n                    } catch (error) {\n                        console.error('Error parsing saved user:', error);\n                    }\n                    try {\n                        parsedConfigUsers = configUsers ? JSON.parse(configUsers) : null;\n                    } catch (error) {\n                        console.error('Error parsing config users:', error);\n                    }\n                    const info = {\n                        currentUser: user,\n                        isAuthenticated,\n                        savedUserInStorage: parsedSavedUser,\n                        configUsersInStorage: parsedConfigUsers,\n                        hasRoles: {\n                            admin_super: hasRole([\n                                'admin_super'\n                            ]),\n                            admin_biasa: hasRole([\n                                'admin_biasa'\n                            ]),\n                            reviewer: hasRole([\n                                'reviewer'\n                            ]),\n                            scoopers: hasRole([\n                                'scoopers'\n                            ]),\n                            karyawan: hasRole([\n                                'karyawan'\n                            ])\n                        },\n                        documentAccess: {\n                            'doc-1': (user === null || user === void 0 ? void 0 : user.role) ? [\n                                'admin_super',\n                                'admin_biasa',\n                                'scoopers'\n                            ].includes(user.role) : false,\n                            'doc-2': (user === null || user === void 0 ? void 0 : user.role) ? [\n                                'admin_super',\n                                'admin_biasa',\n                                'scoopers'\n                            ].includes(user.role) : false,\n                            'doc-3': (user === null || user === void 0 ? void 0 : user.role) ? [\n                                'admin_super',\n                                'admin_biasa',\n                                'scoopers'\n                            ].includes(user.role) : false,\n                            'doc-4': (user === null || user === void 0 ? void 0 : user.role) ? [\n                                'admin_super',\n                                'admin_biasa'\n                            ].includes(user.role) : false\n                        },\n                        timestamp: new Date().toISOString()\n                    };\n                    setDebugInfo(info);\n                }\n            }[\"AuthDebugPanel.useEffect.collectDebugInfo\"];\n            collectDebugInfo();\n            // Update every 2 seconds\n            const interval = setInterval(collectDebugInfo, 2000);\n            return ({\n                \"AuthDebugPanel.useEffect\": ()=>clearInterval(interval)\n            })[\"AuthDebugPanel.useEffect\"];\n        }\n    }[\"AuthDebugPanel.useEffect\"], [\n        user,\n        isAuthenticated,\n        hasRole\n    ]);\n    if (!isVisible) {\n        return null;\n    }\n    const getRoleColor = (role)=>{\n        const colors = {\n            admin_super: 'text-red-600 bg-red-50',\n            admin_biasa: 'text-blue-600 bg-blue-50',\n            reviewer: 'text-green-600 bg-green-50',\n            scoopers: 'text-purple-600 bg-purple-50',\n            karyawan: 'text-gray-600 bg-gray-50'\n        };\n        return colors[role] || 'text-gray-600 bg-gray-50';\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"fixed bottom-4 right-4 z-50\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"bg-white border border-gray-300 rounded-lg shadow-lg max-w-md\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center justify-between p-3 bg-gray-50 border-b cursor-pointer\",\n                    onClick: ()=>setIsExpanded(!isExpanded),\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center space-x-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"w-3 h-3 rounded-full bg-green-500\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Downloads/webmagang/src/components/debug/AuthDebugPanel.tsx\",\n                                    lineNumber: 91,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"font-medium text-sm\",\n                                    children: \"Auth Debug\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Downloads/webmagang/src/components/debug/AuthDebugPanel.tsx\",\n                                    lineNumber: 92,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Downloads/webmagang/src/components/debug/AuthDebugPanel.tsx\",\n                            lineNumber: 90,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            className: \"text-gray-500 hover:text-gray-700\",\n                            children: isExpanded ? '−' : '+'\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Downloads/webmagang/src/components/debug/AuthDebugPanel.tsx\",\n                            lineNumber: 94,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Downloads/webmagang/src/components/debug/AuthDebugPanel.tsx\",\n                    lineNumber: 86,\n                    columnNumber: 9\n                }, this),\n                isExpanded && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"p-3 max-h-96 overflow-y-auto text-xs\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"mb-3\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                    className: \"font-semibold text-gray-700 mb-1\",\n                                    children: \"Current User\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Downloads/webmagang/src/components/debug/AuthDebugPanel.tsx\",\n                                    lineNumber: 104,\n                                    columnNumber: 15\n                                }, this),\n                                user ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"space-y-1\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                \"ID: \",\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"font-mono\",\n                                                    children: user.id\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Downloads/webmagang/src/components/debug/AuthDebugPanel.tsx\",\n                                                    lineNumber: 107,\n                                                    columnNumber: 28\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Downloads/webmagang/src/components/debug/AuthDebugPanel.tsx\",\n                                            lineNumber: 107,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                \"Name: \",\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"font-mono\",\n                                                    children: user.name\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Downloads/webmagang/src/components/debug/AuthDebugPanel.tsx\",\n                                                    lineNumber: 108,\n                                                    columnNumber: 30\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Downloads/webmagang/src/components/debug/AuthDebugPanel.tsx\",\n                                            lineNumber: 108,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                \"Role: \",\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"px-2 py-1 rounded text-xs font-medium \".concat(getRoleColor(user.role)),\n                                                    children: user.role\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Downloads/webmagang/src/components/debug/AuthDebugPanel.tsx\",\n                                                    lineNumber: 110,\n                                                    columnNumber: 27\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Downloads/webmagang/src/components/debug/AuthDebugPanel.tsx\",\n                                            lineNumber: 109,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                \"Authenticated: \",\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: isAuthenticated ? 'text-green-600' : 'text-red-600',\n                                                    children: isAuthenticated ? 'Yes' : 'No'\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Downloads/webmagang/src/components/debug/AuthDebugPanel.tsx\",\n                                                    lineNumber: 114,\n                                                    columnNumber: 39\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Downloads/webmagang/src/components/debug/AuthDebugPanel.tsx\",\n                                            lineNumber: 114,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Downloads/webmagang/src/components/debug/AuthDebugPanel.tsx\",\n                                    lineNumber: 106,\n                                    columnNumber: 17\n                                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-red-600\",\n                                    children: \"No user logged in\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Downloads/webmagang/src/components/debug/AuthDebugPanel.tsx\",\n                                    lineNumber: 119,\n                                    columnNumber: 17\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Downloads/webmagang/src/components/debug/AuthDebugPanel.tsx\",\n                            lineNumber: 103,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"mb-3\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                    className: \"font-semibold text-gray-700 mb-1\",\n                                    children: \"Role Permissions\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Downloads/webmagang/src/components/debug/AuthDebugPanel.tsx\",\n                                    lineNumber: 125,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"grid grid-cols-2 gap-1\",\n                                    children: Object.entries(debugInfo.hasRoles || {}).map((param)=>{\n                                        let [role, hasAccess] = param;\n                                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center space-x-1\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"w-2 h-2 rounded-full \".concat(hasAccess ? 'bg-green-500' : 'bg-gray-300')\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Downloads/webmagang/src/components/debug/AuthDebugPanel.tsx\",\n                                                    lineNumber: 129,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-xs\",\n                                                    children: role\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Downloads/webmagang/src/components/debug/AuthDebugPanel.tsx\",\n                                                    lineNumber: 130,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, role, true, {\n                                            fileName: \"/Users/<USER>/Downloads/webmagang/src/components/debug/AuthDebugPanel.tsx\",\n                                            lineNumber: 128,\n                                            columnNumber: 19\n                                        }, this);\n                                    })\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Downloads/webmagang/src/components/debug/AuthDebugPanel.tsx\",\n                                    lineNumber: 126,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Downloads/webmagang/src/components/debug/AuthDebugPanel.tsx\",\n                            lineNumber: 124,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"mb-3\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                    className: \"font-semibold text-gray-700 mb-1\",\n                                    children: \"Document Access\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Downloads/webmagang/src/components/debug/AuthDebugPanel.tsx\",\n                                    lineNumber: 138,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"space-y-1\",\n                                    children: Object.entries(debugInfo.documentAccess || {}).map((param)=>{\n                                        let [doc, canAccess] = param;\n                                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center justify-between\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-xs\",\n                                                    children: doc\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Downloads/webmagang/src/components/debug/AuthDebugPanel.tsx\",\n                                                    lineNumber: 142,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-xs \".concat(canAccess ? 'text-green-600' : 'text-red-600'),\n                                                    children: canAccess ? '✓' : '✗'\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Downloads/webmagang/src/components/debug/AuthDebugPanel.tsx\",\n                                                    lineNumber: 143,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, doc, true, {\n                                            fileName: \"/Users/<USER>/Downloads/webmagang/src/components/debug/AuthDebugPanel.tsx\",\n                                            lineNumber: 141,\n                                            columnNumber: 19\n                                        }, this);\n                                    })\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Downloads/webmagang/src/components/debug/AuthDebugPanel.tsx\",\n                                    lineNumber: 139,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Downloads/webmagang/src/components/debug/AuthDebugPanel.tsx\",\n                            lineNumber: 137,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"mb-3\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                    className: \"font-semibold text-gray-700 mb-1\",\n                                    children: \"Storage Info\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Downloads/webmagang/src/components/debug/AuthDebugPanel.tsx\",\n                                    lineNumber: 153,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"space-y-1\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                \"Saved User: \",\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: debugInfo.savedUserInStorage ? 'text-green-600' : 'text-red-600',\n                                                    children: debugInfo.savedUserInStorage ? 'Found' : 'Not found'\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Downloads/webmagang/src/components/debug/AuthDebugPanel.tsx\",\n                                                    lineNumber: 156,\n                                                    columnNumber: 31\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Downloads/webmagang/src/components/debug/AuthDebugPanel.tsx\",\n                                            lineNumber: 155,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                \"Config Users: \",\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: debugInfo.configUsersInStorage ? 'text-green-600' : 'text-red-600',\n                                                    children: debugInfo.configUsersInStorage ? 'Found' : 'Not found'\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Downloads/webmagang/src/components/debug/AuthDebugPanel.tsx\",\n                                                    lineNumber: 161,\n                                                    columnNumber: 33\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Downloads/webmagang/src/components/debug/AuthDebugPanel.tsx\",\n                                            lineNumber: 160,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Downloads/webmagang/src/components/debug/AuthDebugPanel.tsx\",\n                                    lineNumber: 154,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Downloads/webmagang/src/components/debug/AuthDebugPanel.tsx\",\n                            lineNumber: 152,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"border-t pt-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                    className: \"font-semibold text-gray-700 mb-1\",\n                                    children: \"Quick Actions\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Downloads/webmagang/src/components/debug/AuthDebugPanel.tsx\",\n                                    lineNumber: 170,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"space-y-1\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: ()=>{\n                                                localStorage.clear();\n                                                window.location.reload();\n                                            },\n                                            className: \"w-full text-left text-xs text-red-600 hover:text-red-800 p-1 hover:bg-red-50 rounded\",\n                                            children: \"Clear Storage & Reload\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Downloads/webmagang/src/components/debug/AuthDebugPanel.tsx\",\n                                            lineNumber: 172,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: ()=>{\n                                                console.log('Debug Info:', debugInfo);\n                                            },\n                                            className: \"w-full text-left text-xs text-blue-600 hover:text-blue-800 p-1 hover:bg-blue-50 rounded\",\n                                            children: \"Log Debug Info\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Downloads/webmagang/src/components/debug/AuthDebugPanel.tsx\",\n                                            lineNumber: 181,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: ()=>{\n                                                window.location.href = '/login';\n                                            },\n                                            className: \"w-full text-left text-xs text-green-600 hover:text-green-800 p-1 hover:bg-green-50 rounded\",\n                                            children: \"Go to Login\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Downloads/webmagang/src/components/debug/AuthDebugPanel.tsx\",\n                                            lineNumber: 189,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Downloads/webmagang/src/components/debug/AuthDebugPanel.tsx\",\n                                    lineNumber: 171,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Downloads/webmagang/src/components/debug/AuthDebugPanel.tsx\",\n                            lineNumber: 169,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-xs text-gray-500 mt-2 pt-2 border-t\",\n                            children: [\n                                \"Last updated: \",\n                                new Date(debugInfo.timestamp).toLocaleTimeString()\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Downloads/webmagang/src/components/debug/AuthDebugPanel.tsx\",\n                            lineNumber: 201,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Downloads/webmagang/src/components/debug/AuthDebugPanel.tsx\",\n                    lineNumber: 101,\n                    columnNumber: 11\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"/Users/<USER>/Downloads/webmagang/src/components/debug/AuthDebugPanel.tsx\",\n            lineNumber: 84,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Downloads/webmagang/src/components/debug/AuthDebugPanel.tsx\",\n        lineNumber: 83,\n        columnNumber: 5\n    }, this);\n}\n_s(AuthDebugPanel, \"61PHw/kXYwWw8LdtDOJCbJrES9s=\", false, function() {\n    return [\n        _store_auth__WEBPACK_IMPORTED_MODULE_2__.useAuth\n    ];\n});\n_c = AuthDebugPanel;\n// Hook untuk mengaktifkan debug panel\nfunction useAuthDebug() {\n    _s1();\n    const [isDebugVisible, setIsDebugVisible] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"useAuthDebug.useEffect\": ()=>{\n            const handleKeyPress = {\n                \"useAuthDebug.useEffect.handleKeyPress\": (event)=>{\n                    // Ctrl + Shift + D untuk toggle debug panel\n                    if (event.ctrlKey && event.shiftKey && event.key === 'D') {\n                        setIsDebugVisible({\n                            \"useAuthDebug.useEffect.handleKeyPress\": (prev)=>!prev\n                        }[\"useAuthDebug.useEffect.handleKeyPress\"]);\n                    }\n                }\n            }[\"useAuthDebug.useEffect.handleKeyPress\"];\n            window.addEventListener('keydown', handleKeyPress);\n            return ({\n                \"useAuthDebug.useEffect\": ()=>{\n                    window.removeEventListener('keydown', handleKeyPress);\n                }\n            })[\"useAuthDebug.useEffect\"];\n        }\n    }[\"useAuthDebug.useEffect\"], []);\n    return {\n        isDebugVisible,\n        setIsDebugVisible\n    };\n}\n_s1(useAuthDebug, \"A4hqQBxHou8W9f3Vi8NIjdsrUSE=\");\nvar _c;\n$RefreshReg$(_c, \"AuthDebugPanel\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/debug/AuthDebugPanel.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/components/layout/AppLayout.tsx":
/*!*********************************************!*\
  !*** ./src/components/layout/AppLayout.tsx ***!
  \*********************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ AppLayout)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var _store_auth__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/store/auth */ \"(app-pages-browser)/./src/store/auth.ts\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! next/image */ \"(app-pages-browser)/./node_modules/next/dist/api/image.js\");\n/* harmony import */ var _barrel_optimize_names_Bars3Icon_CalendarIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=Bars3Icon,CalendarIcon,XMarkIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/CalendarIcon.js\");\n/* harmony import */ var _barrel_optimize_names_Bars3Icon_CalendarIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=Bars3Icon,CalendarIcon,XMarkIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/XMarkIcon.js\");\n/* harmony import */ var _barrel_optimize_names_Bars3Icon_CalendarIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=Bars3Icon,CalendarIcon,XMarkIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/Bars3Icon.js\");\n/* harmony import */ var _contexts_ProgramContext__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/contexts/ProgramContext */ \"(app-pages-browser)/./src/contexts/ProgramContext.tsx\");\n/* harmony import */ var _components_debug_AuthDebugPanel__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/debug/AuthDebugPanel */ \"(app-pages-browser)/./src/components/debug/AuthDebugPanel.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$(), _s1 = $RefreshSig$();\n\n\n\n\n\n\n\nfunction ProgramIndicator() {\n    _s();\n    const { activeProgram } = (0,_contexts_ProgramContext__WEBPACK_IMPORTED_MODULE_5__.useProgram)();\n    if (!activeProgram) return null;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"hidden md:flex items-center space-x-2 px-3 py-1 bg-blue-50 border border-blue-200 rounded-lg\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bars3Icon_CalendarIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                className: \"h-4 w-4 text-blue-600\"\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Downloads/webmagang/src/components/layout/AppLayout.tsx\",\n                lineNumber: 22,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                className: \"text-sm font-medium text-blue-700\",\n                children: activeProgram.name\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Downloads/webmagang/src/components/layout/AppLayout.tsx\",\n                lineNumber: 23,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Downloads/webmagang/src/components/layout/AppLayout.tsx\",\n        lineNumber: 21,\n        columnNumber: 5\n    }, this);\n}\n_s(ProgramIndicator, \"g/tdYF8axooZDa+8hPRSOzOtkns=\", false, function() {\n    return [\n        _contexts_ProgramContext__WEBPACK_IMPORTED_MODULE_5__.useProgram\n    ];\n});\n_c = ProgramIndicator;\nfunction AppLayout(param) {\n    let { children } = param;\n    _s1();\n    const { user, logout } = (0,_store_auth__WEBPACK_IMPORTED_MODULE_1__.useAuth)();\n    const [isMobileMenuOpen, setIsMobileMenuOpen] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    const pathname = (0,next_navigation__WEBPACK_IMPORTED_MODULE_3__.usePathname)();\n    const { isDebugVisible } = (0,_components_debug_AuthDebugPanel__WEBPACK_IMPORTED_MODULE_6__.useAuthDebug)();\n    // Function to determine if a menu item is active\n    const isActive = (path)=>{\n        if (path === '/dashboard') {\n            return pathname === '/dashboard';\n        }\n        return pathname.startsWith(path);\n    };\n    // Function to get nav link classes\n    const getNavLinkClasses = function(path) {\n        let isMobile = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : false;\n        const baseClasses = \"font-gotham-rounded font-medium transition-colors duration-200\";\n        const mobileClasses = isMobile ? \"block px-3 py-2 rounded-md text-base\" : \"\";\n        if (isActive(path)) {\n            // Active state classes\n            const activeClasses = isMobile ? \"text-primary bg-blue-50 border-l-4 border-primary\" : \"text-primary border-b-2 border-primary\";\n            return \"\".concat(baseClasses, \" \").concat(mobileClasses, \" \").concat(activeClasses);\n        } else {\n            // Inactive state classes\n            const inactiveClasses = isMobile ? \"text-secondary hover:text-primary hover:bg-gray-50\" : \"text-secondary hover:text-primary\";\n            return \"\".concat(baseClasses, \" \").concat(mobileClasses, \" \").concat(inactiveClasses);\n        }\n    };\n    const toggleMobileMenu = ()=>{\n        setIsMobileMenuOpen(!isMobileMenuOpen);\n    };\n    const closeMobileMenu = ()=>{\n        setIsMobileMenuOpen(false);\n    };\n    if (!user) {\n        // Redirect to login instead of showing loading\n        if (true) {\n            window.location.href = '/login';\n        }\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen flex items-center justify-center bg-gray-50\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"animate-spin rounded-full h-12 w-12 border-b-2 border-red-500 mx-auto\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Downloads/webmagang/src/components/layout/AppLayout.tsx\",\n                        lineNumber: 82,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"mt-4 text-gray-600\",\n                        children: \"Mengarahkan ke halaman login...\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Downloads/webmagang/src/components/layout/AppLayout.tsx\",\n                        lineNumber: 83,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Downloads/webmagang/src/components/layout/AppLayout.tsx\",\n                lineNumber: 81,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/Downloads/webmagang/src/components/layout/AppLayout.tsx\",\n            lineNumber: 80,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-gray-50\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                className: \"fixed top-0 left-0 right-0 z-50 bg-white shadow-lg border-b border-gray-200\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex justify-between h-16\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center space-x-8\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                src: \"/smap-logo.png\",\n                                                alt: \"SMAP Logo\",\n                                                width: 120,\n                                                height: 48,\n                                                className: \"h-12 w-auto transition-transform duration-200 hover:scale-105\",\n                                                priority: true\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Downloads/webmagang/src/components/layout/AppLayout.tsx\",\n                                                lineNumber: 97,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Downloads/webmagang/src/components/layout/AppLayout.tsx\",\n                                            lineNumber: 96,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"hidden md:flex space-x-8\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                    href: \"/dashboard\",\n                                                    className: \"font-gotham-rounded font-medium text-secondary hover:text-primary transition-colors duration-200\",\n                                                    children: \"Dashboard\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Downloads/webmagang/src/components/layout/AppLayout.tsx\",\n                                                    lineNumber: 111,\n                                                    columnNumber: 17\n                                                }, this),\n                                                (user.role === 'admin_super' || user.role === 'admin_biasa') && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                            href: \"/config\",\n                                                            className: \"font-gotham-rounded font-medium text-secondary hover:text-primary transition-colors duration-200\",\n                                                            children: \"Config\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Downloads/webmagang/src/components/layout/AppLayout.tsx\",\n                                                            lineNumber: 121,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                            href: \"/manage-group\",\n                                                            className: \"font-gotham-rounded font-medium text-secondary hover:text-primary transition-colors duration-200\",\n                                                            children: \"Manage Group\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Downloads/webmagang/src/components/layout/AppLayout.tsx\",\n                                                            lineNumber: 127,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true),\n                                                user.role === 'admin_super' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                    href: \"/manage-program\",\n                                                    className: \"font-gotham-rounded font-medium text-secondary hover:text-primary transition-colors duration-200\",\n                                                    children: \"Manage Program\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Downloads/webmagang/src/components/layout/AppLayout.tsx\",\n                                                    lineNumber: 138,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                    href: \"/repository\",\n                                                    className: \"font-gotham-rounded font-medium text-secondary hover:text-primary transition-colors duration-200\",\n                                                    children: \"Risk Assessment\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Downloads/webmagang/src/components/layout/AppLayout.tsx\",\n                                                    lineNumber: 147,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Downloads/webmagang/src/components/layout/AppLayout.tsx\",\n                                            lineNumber: 110,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Downloads/webmagang/src/components/layout/AppLayout.tsx\",\n                                    lineNumber: 95,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center space-x-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ProgramIndicator, {}, void 0, false, {\n                                            fileName: \"/Users/<USER>/Downloads/webmagang/src/components/layout/AppLayout.tsx\",\n                                            lineNumber: 158,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"hidden md:flex items-center space-x-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"font-gotham text-sm text-secondary\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"font-medium\",\n                                                            children: user.name\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Downloads/webmagang/src/components/layout/AppLayout.tsx\",\n                                                            lineNumber: 163,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-xs ml-1\",\n                                                            children: [\n                                                                \"(\",\n                                                                (0,_store_auth__WEBPACK_IMPORTED_MODULE_1__.getRoleDisplayName)(user.role),\n                                                                \")\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Users/<USER>/Downloads/webmagang/src/components/layout/AppLayout.tsx\",\n                                                            lineNumber: 164,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Downloads/webmagang/src/components/layout/AppLayout.tsx\",\n                                                    lineNumber: 162,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    onClick: ()=>{\n                                                        logout();\n                                                        window.location.href = '/login';\n                                                    },\n                                                    className: \"font-gotham-rounded font-medium text-sm text-primary hover:text-primary-700 transition-colors duration-200\",\n                                                    children: \"Logout\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Downloads/webmagang/src/components/layout/AppLayout.tsx\",\n                                                    lineNumber: 166,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Downloads/webmagang/src/components/layout/AppLayout.tsx\",\n                                            lineNumber: 161,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"md:hidden\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: toggleMobileMenu,\n                                                className: \"inline-flex items-center justify-center p-2 rounded-md text-gray-400 hover:text-gray-500 hover:bg-gray-100 focus:outline-none focus:ring-2 focus:ring-inset focus:ring-primary\",\n                                                \"aria-expanded\": \"false\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"sr-only\",\n                                                        children: \"Open main menu\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Downloads/webmagang/src/components/layout/AppLayout.tsx\",\n                                                        lineNumber: 184,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    isMobileMenuOpen ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bars3Icon_CalendarIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                        className: \"block h-6 w-6\",\n                                                        \"aria-hidden\": \"true\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Downloads/webmagang/src/components/layout/AppLayout.tsx\",\n                                                        lineNumber: 186,\n                                                        columnNumber: 21\n                                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bars3Icon_CalendarIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                        className: \"block h-6 w-6\",\n                                                        \"aria-hidden\": \"true\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Downloads/webmagang/src/components/layout/AppLayout.tsx\",\n                                                        lineNumber: 188,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Downloads/webmagang/src/components/layout/AppLayout.tsx\",\n                                                lineNumber: 179,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Downloads/webmagang/src/components/layout/AppLayout.tsx\",\n                                            lineNumber: 178,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Downloads/webmagang/src/components/layout/AppLayout.tsx\",\n                                    lineNumber: 156,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Downloads/webmagang/src/components/layout/AppLayout.tsx\",\n                            lineNumber: 94,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Downloads/webmagang/src/components/layout/AppLayout.tsx\",\n                        lineNumber: 93,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"md:hidden \".concat(isMobileMenuOpen ? 'block' : 'hidden'),\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"px-2 pt-2 pb-3 space-y-1 sm:px-3 bg-white border-t border-gray-200 shadow-lg\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                    href: \"/dashboard\",\n                                    onClick: closeMobileMenu,\n                                    className: \"font-gotham-rounded font-medium text-secondary hover:text-primary hover:bg-gray-50 block px-3 py-2 rounded-md text-base transition-colors duration-200\",\n                                    children: \"Dashboard\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Downloads/webmagang/src/components/layout/AppLayout.tsx\",\n                                    lineNumber: 199,\n                                    columnNumber: 13\n                                }, this),\n                                (user.role === 'admin_super' || user.role === 'admin_biasa') && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                            href: \"/config\",\n                                            onClick: closeMobileMenu,\n                                            className: \"font-gotham-rounded font-medium text-secondary hover:text-primary hover:bg-gray-50 block px-3 py-2 rounded-md text-base transition-colors duration-200\",\n                                            children: \"Config\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Downloads/webmagang/src/components/layout/AppLayout.tsx\",\n                                            lineNumber: 210,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                            href: \"/manage-group\",\n                                            onClick: closeMobileMenu,\n                                            className: \"font-gotham-rounded font-medium text-secondary hover:text-primary hover:bg-gray-50 block px-3 py-2 rounded-md text-base transition-colors duration-200\",\n                                            children: \"Manage Group\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Downloads/webmagang/src/components/layout/AppLayout.tsx\",\n                                            lineNumber: 217,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true),\n                                user.role === 'admin_super' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                    href: \"/manage-program\",\n                                    onClick: closeMobileMenu,\n                                    className: \"font-gotham-rounded font-medium text-secondary hover:text-primary hover:bg-gray-50 block px-3 py-2 rounded-md text-base transition-colors duration-200\",\n                                    children: \"Manage Program\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Downloads/webmagang/src/components/layout/AppLayout.tsx\",\n                                    lineNumber: 229,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                    href: \"/repository\",\n                                    onClick: closeMobileMenu,\n                                    className: \"font-gotham-rounded font-medium text-secondary hover:text-primary hover:bg-gray-50 block px-3 py-2 rounded-md text-base transition-colors duration-200\",\n                                    children: \"Risk Assessment\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Downloads/webmagang/src/components/layout/AppLayout.tsx\",\n                                    lineNumber: 239,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"border-t border-gray-200 pt-4 pb-3\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"px-3\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-base font-medium text-gray-800\",\n                                                    children: user.name\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Downloads/webmagang/src/components/layout/AppLayout.tsx\",\n                                                    lineNumber: 250,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-sm text-gray-500\",\n                                                    children: [\n                                                        \"(\",\n                                                        (0,_store_auth__WEBPACK_IMPORTED_MODULE_1__.getRoleDisplayName)(user.role),\n                                                        \")\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Downloads/webmagang/src/components/layout/AppLayout.tsx\",\n                                                    lineNumber: 251,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Downloads/webmagang/src/components/layout/AppLayout.tsx\",\n                                            lineNumber: 249,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"mt-3 px-3\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: ()=>{\n                                                    logout();\n                                                    window.location.href = '/login';\n                                                },\n                                                className: \"font-gotham-rounded font-medium text-sm text-primary hover:text-primary-700 transition-colors duration-200 w-full text-left\",\n                                                children: \"Logout\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Downloads/webmagang/src/components/layout/AppLayout.tsx\",\n                                                lineNumber: 254,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Downloads/webmagang/src/components/layout/AppLayout.tsx\",\n                                            lineNumber: 253,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Downloads/webmagang/src/components/layout/AppLayout.tsx\",\n                                    lineNumber: 248,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Downloads/webmagang/src/components/layout/AppLayout.tsx\",\n                            lineNumber: 198,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Downloads/webmagang/src/components/layout/AppLayout.tsx\",\n                        lineNumber: 197,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Downloads/webmagang/src/components/layout/AppLayout.tsx\",\n                lineNumber: 92,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n                className: \"pt-24 pb-8\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\",\n                    children: children\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Downloads/webmagang/src/components/layout/AppLayout.tsx\",\n                    lineNumber: 271,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Downloads/webmagang/src/components/layout/AppLayout.tsx\",\n                lineNumber: 270,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Downloads/webmagang/src/components/layout/AppLayout.tsx\",\n        lineNumber: 90,\n        columnNumber: 5\n    }, this);\n}\n_s1(AppLayout, \"MD3k9S5ZJ1Ac6C6G5BI1qVEE6l8=\", false, function() {\n    return [\n        _store_auth__WEBPACK_IMPORTED_MODULE_1__.useAuth,\n        next_navigation__WEBPACK_IMPORTED_MODULE_3__.usePathname,\n        _components_debug_AuthDebugPanel__WEBPACK_IMPORTED_MODULE_6__.useAuthDebug\n    ];\n});\n_c1 = AppLayout;\nvar _c, _c1;\n$RefreshReg$(_c, \"ProgramIndicator\");\n$RefreshReg$(_c1, \"AppLayout\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/layout/AppLayout.tsx\n"));

/***/ })

});