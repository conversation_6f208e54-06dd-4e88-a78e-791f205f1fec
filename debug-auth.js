// Debug script untuk memeriksa authentication dan role Scoopers
// Jalankan di browser console

console.log('🔍 DEBUGGING AUTHENTICATION & ROLE SCOOPERS');
console.log('='.repeat(50));

// 1. Check localStorage untuk user data
console.log('1. Checking localStorage...');
const savedUser = localStorage.getItem('smap_user');
console.log('Saved user in localStorage:', savedUser);

if (savedUser) {
  try {
    const parsedUser = JSON.parse(savedUser);
    console.log('Parsed user object:', parsedUser);
    console.log('User role:', parsedUser.role);
    console.log('Is role scoopers?', parsedUser.role === 'scoopers');
  } catch (error) {
    console.error('Error parsing saved user:', error);
  }
}

// 2. Check default users configuration
console.log('\n2. Checking default users configuration...');
const DEFAULT_USERS = {
  '1001': {
    id: '1',
    name: '1001',
    email: '',
    role: 'admin_super',
    lastLogin: new Date(),
    password: '1001',
  },
  '1002': {
    id: '2',
    name: '1002',
    email: '',
    role: 'admin_biasa',
    lastLogin: new Date(),
    password: '1002',
  },
  '1003': {
    id: '3',
    name: '1003',
    email: '',
    role: 'reviewer',
    lastLogin: new Date(),
    password: '1003',
  },
  '1004': {
    id: '4',
    name: '1004',
    email: '',
    role: 'scoopers',
    lastLogin: new Date(),
    password: '1004',
  },
  '1005': {
    id: '5',
    name: '1005',
    email: '',
    role: 'karyawan',
    lastLogin: new Date(),
    password: '1005',
  },
};

console.log('Default user 1004 (Scoopers):', DEFAULT_USERS['1004']);

// 3. Check config-users in localStorage
console.log('\n3. Checking config-users in localStorage...');
const configUsers = localStorage.getItem('config-users');
console.log('Config users in localStorage:', configUsers);

if (configUsers) {
  try {
    const parsedConfigUsers = JSON.parse(configUsers);
    console.log('Parsed config users:', parsedConfigUsers);
    
    const scoopersUser = parsedConfigUsers.find(user => user.role === 'scoopers');
    console.log('Scoopers user in config:', scoopersUser);
  } catch (error) {
    console.error('Error parsing config users:', error);
  }
}

// 4. Test login function simulation
console.log('\n4. Testing login function simulation...');

function testLogin(username, password) {
  console.log(`Testing login for username: ${username}, password: ${password}`);
  
  // Get users from localStorage or default
  let users = DEFAULT_USERS;
  
  const savedConfigUsers = localStorage.getItem('config-users');
  if (savedConfigUsers) {
    try {
      const parsedUsers = JSON.parse(savedConfigUsers);
      const usersRecord = {};
      
      parsedUsers.forEach((user) => {
        usersRecord[user.name] = {
          id: user.id,
          name: user.name,
          email: user.email || '',
          role: user.role,
          lastLogin: new Date(user.lastLogin),
          password: user.password || user.name
        };
      });
      
      users = usersRecord;
      console.log('Using config users from localStorage');
    } catch (error) {
      console.error('Error parsing config users, using default:', error);
    }
  } else {
    console.log('Using default users');
  }
  
  const user = users[username];
  console.log('Found user:', user);
  
  if (user && password === username) {
    console.log('✅ Login would succeed');
    console.log('User role:', user.role);
    return user;
  } else {
    console.log('❌ Login would fail');
    return null;
  }
}

// Test login untuk user 1004 (Scoopers)
const testResult = testLogin('1004', '1004');

// 5. Check role validation
console.log('\n5. Testing role validation...');

function testRoleValidation(userRole) {
  console.log(`Testing role validation for: ${userRole}`);
  
  // Test hasRole function
  const testRoles = ['admin_super', 'admin_biasa', 'reviewer', 'scoopers', 'karyawan'];
  
  testRoles.forEach(role => {
    const hasRole = [role].includes(userRole);
    console.log(`Has role ${role}:`, hasRole);
  });
  
  // Test document access
  const canAccessDoc1 = ['admin_super', 'admin_biasa', 'scoopers'].includes(userRole);
  const canAccessDoc2 = ['admin_super', 'admin_biasa', 'scoopers'].includes(userRole);
  const canAccessDoc3 = ['admin_super', 'admin_biasa', 'scoopers'].includes(userRole);
  
  console.log('Can access doc-1:', canAccessDoc1);
  console.log('Can access doc-2:', canAccessDoc2);
  console.log('Can access doc-3:', canAccessDoc3);
}

if (testResult) {
  testRoleValidation(testResult.role);
}

// 6. Check current window location and auth state
console.log('\n6. Checking current state...');
console.log('Current URL:', window.location.href);
console.log('Current pathname:', window.location.pathname);

// Try to access Zustand store if available
if (window.__ZUSTAND_STORES__) {
  console.log('Zustand stores available:', Object.keys(window.__ZUSTAND_STORES__));
}

// 7. Recommendations
console.log('\n7. RECOMMENDATIONS:');
console.log('='.repeat(30));

if (!savedUser) {
  console.log('❌ No user logged in. Please login with username: 1004, password: 1004');
} else {
  const parsedUser = JSON.parse(savedUser);
  if (parsedUser.role !== 'scoopers') {
    console.log(`❌ Current user role is: ${parsedUser.role}. Please login with Scoopers account (1004/1004)`);
  } else {
    console.log('✅ User is logged in as Scoopers. Role should be working correctly.');
    console.log('If sections are not showing, check:');
    console.log('- SUB_DOCUMENTS array in doc-2 and doc-3');
    console.log('- getAllSections() function');
    console.log('- Component rendering logic');
  }
}

console.log('\n🔧 To fix issues:');
console.log('1. Clear localStorage: localStorage.clear()');
console.log('2. Login with 1004/1004');
console.log('3. Check browser console for errors');
console.log('4. Verify component props and state');

console.log('\n✅ Debug script completed!');
